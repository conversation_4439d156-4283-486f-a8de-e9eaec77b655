Metadata-Version: 2.1
Name: ddddocr
Version: 1.0.6
Summary: 带带弟弟OCR
Home-page: https://github.com/sml2h3/ddddocr
Author: sml2h3
License: UNKNOWN
Platform: UNKNOWN
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.8
Description-Content-Type: text/markdown
Requires-Dist: numpy
Requires-Dist: onnxruntime
Requires-Dist: Pillow

![header.png](https://z3.ax1x.com/2021/07/02/R6Ih28.jpg)

# 带带弟弟OCR通用验证码识别SDK免费开源版

## 环境要求

`python >= 3.8`

`Windows/Linux/Macox..`

## 调用方法

`pip install ddddocr`

```
import ddddocr

ocr = ddddocr.DdddOcr()

with open('test.png', 'rb') as f:

    img_bytes = f.read()

res = ocr.classification(f.read())

print(res)
```

### 参数说明

`DdddOcr 接受两个参数`

|  参数名   | 默认值  | 说明  |
|  ----  | ----  | ----  |
| use_gpu  | False | Bool    是否使用gpu进行推理，如果该值为False则device_id不生效 |
| device_id  | 0 | int cuda设备号，目前仅支持单张显卡 |

`classification`

|  参数名   | 默认值  | 说明  |
|  ----  | ----  | ----  |
| img  | 0 | bytes 图片的bytes格式 |

