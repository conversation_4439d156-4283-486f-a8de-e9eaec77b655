# YngpCrawler_old 改造总结

## 改造目标

根据用户需求，对 `YngpCrawler_old.java` 进行了以下两个主要改造：

1. **为每个项目创建专门的目录**：目录名为项目编号（如：KMZC2025-G1-01997-YNZZ-0098）
2. **遍历下载所有附件**：遍历 `<table class="table table-hover" id="filelist">` 表格中的所有附件并逐一下载，而不是只下载一个文件

## 主要改造内容

### 1. 项目编号提取功能

**新增方法：`extractProjectNumber(Page page)`**
- 从HTML页面中提取项目编号
- 使用正则表达式匹配格式：`项目编号[：:][\\s]*([A-Z0-9\\-]+)`
- 支持中英文冒号和空格
- 示例匹配：`项目编号：KMZC2025-G1-01997-YNZZ-0098`

### 2. 目录创建逻辑改造

**修改方法：`downloadAttachments(Page page, String title, String projectNumber, Options options)`**
- 新增 `projectNumber` 参数
- 为每个项目创建以项目编号命名的子目录
- 目录结构：`基础下载目录/项目编号/`
- 如果无法获取项目编号，使用默认目录：`unknown_project_时间戳`

### 3. 多文件下载逻辑

**核心改造：移除单文件下载限制**
- 原逻辑：下载一个文件后设置 `completed.set(true)` 停止
- 新逻辑：遍历所有附件，每个都尝试下载

**新增专门的 filelist 表格处理：`processFileListTable()`**
- 专门查找和处理 `table#filelist` 表格
- 遍历表格中所有下载链接：`a[href*='javascript:download'], a:has-text('下载')`
- 逐一尝试下载每个附件

**新增多文件版本的处理方法：**
- `processPageDirectLinksMultiple()` - 处理页面直链（多文件版本）
- `processPageTextLinksMultiple()` - 处理页面文本链接（多文件版本）
- `processFrameDirectLinksMultiple()` - 处理Frame直链（多文件版本）
- `processFrameTextLinksMultiple()` - 处理Frame文本链接（多文件版本）
- `processLocatorSetWithDownloadMultiple()` - 处理定位器集合（多文件版本）
- `attemptClickDownloadMultiple()` - 尝试点击下载（多文件版本）
- `registerDownloadHooksForMultiple()` - 注册下载钩子（多文件版本）

### 4. 调用链改造

**修改 `processLinksFile()` 方法：**
```java
// 原调用
downloadAttachments(detailPage, title, options);

// 新调用
String projectNumber = extractProjectNumber(detailPage);
downloadAttachments(detailPage, title, projectNumber, options);
```

## 下载策略优先级

改造后的下载策略按以下优先级执行：

1. **专门处理 filelist 表格**：优先查找并处理 `table#filelist` 中的所有附件
2. **页面直链下载**：如果表格中没有找到附件，尝试页面中的直接下载链接
3. **文本链接下载**：尝试基于文本的下载入口（如"下载"、"附件"等按钮）
4. **Frame内容处理**：尝试iframe内部的相同策略

## 测试验证

创建了 `YngpCrawlerOldTest.java` 测试类，验证：
- ✅ 项目编号正则表达式匹配功能
- ✅ 文件名清理功能
- ✅ 编译通过，无语法错误

## 兼容性

- 保持了原有的所有功能和方法
- 向后兼容，不影响现有的调用方式
- 新增的多文件下载功能是在原有基础上的增强

## 使用示例

改造后，爬虫会：
1. 访问详情页面
2. 提取项目编号（如：KMZC2025-G1-01997-YNZZ-0098）
3. 创建项目目录：`下载根目录/KMZC2025-G1-01997-YNZZ-0098/`
4. 遍历 filelist 表格中的所有附件
5. 将所有附件下载到项目目录中

这样每个项目的所有附件都会被整齐地组织在以项目编号命名的专门目录中。
