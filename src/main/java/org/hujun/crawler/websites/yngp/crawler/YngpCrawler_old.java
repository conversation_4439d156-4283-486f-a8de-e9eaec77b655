package org.hujun.crawler.websites.yngp.crawler;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.Cookie;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;

// OCR HTTP 请求已抽取到 OcrClient

/**
 * 云南省政府采购网爬虫（yngp）
 * 模式：仅支持“从链接文件逐条打开详情页并下载附件”。
 *
 * 注意：
 * - 若出现验证码，统一按“数字/字母图片验证码”处理，仅走 OCR 自动识别，不做人工或滑块处理。
 */
public class YngpCrawler_old {

    private static final Logger logger = LoggerFactory.getLogger(YngpCrawler_old.class);

    // 列表页流程已移除，仅保留“链接文件直达详情”模式

    private static final double DEFAULT_TIMEOUT = 60000;

    public static class Options {
        public boolean headless = true;
        public String ocrEndpoint = ""; // OCR 服务地址，如 https://xxx.ngrok-free.app
        public String outputDir = ""; // 自定义输出根目录（可选）
        public String linksFile = ""; // 直接从该文件读取详情链接（每行一个URL）
    }

    public void run(Options options) {
        logger.info("启动 YNGP 爬虫（直连详情模式），参数：headless={}, linksFile='{}'",
                options.headless, options.linksFile);

        try (Playwright playwright = Playwright.create()) {
            Browser browser = playwright.chromium()
                    .launch(new BrowserType.LaunchOptions().setHeadless(options.headless));

            // 初始化基准目录与下载目录
            this.resolvedBaseDir = resolveBaseDir(options);
            Path downloadDir = getDownloadDir();
            try {
                Files.createDirectories(downloadDir);
                logger.info("设置下载目录: {}", downloadDir.toAbsolutePath());
            } catch (Exception e) {
                logger.error("创建下载目录失败: {}", e.getMessage());
                return;
            }

            BrowserContext context = browser.newContext(new Browser.NewContextOptions()
                    .setAcceptDownloads(true));

            // 仅支持“链接文件直达详情”模式
            Path linksPath = resolveLinksPath(options);
            if (linksPath == null || !Files.exists(linksPath)) {
                String hint = (linksPath == null) ? "(null)" : linksPath.toAbsolutePath().toString();
                logger.error("未找到链接文件，已取消执行: {}", hint);
                return;
            }
            logger.info("启用直连详情模式: {}", linksPath.toAbsolutePath());
            processLinksFile(context, linksPath, options);

            context.close();
            logger.info("YNGP 爬虫完成");
        } catch (Exception e) {
            logger.error("YNGP 爬虫执行失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 解析链接文件路径：优先 Options.linksFile；否则默认使用 基础目录/files/collected_links.txt
     */
    private Path resolveLinksPath(Options options) {
        try {
            if (options != null && options.linksFile != null && !options.linksFile.isBlank()) {
                Path p = Paths.get(options.linksFile.trim());
                return p.isAbsolute() ? p : p.toAbsolutePath();
            }
            // 默认路径：与下载目录同级
            return getDownloadDir().resolve("collected_links.txt");
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 读取链接文件并逐条处理：打开详情页、保存HTML、触发附件下载。
     */
    private void processLinksFile(BrowserContext context, Path linksPath, Options options) throws Exception {
        List<String> lines;
        try {
            lines = Files.readAllLines(linksPath, StandardCharsets.UTF_8);
        } catch (Exception e) {
            logger.error("读取链接文件失败: {}", e.getMessage());
            return;
        }

        int index = 0;
        for (String raw : lines) {
            String url = raw == null ? null : raw.trim();
            if (url == null || url.isEmpty())
                continue;
            if (url.startsWith("#"))
                continue;

            index++;
            logger.info("[links] 处理第 {} 条: {}", index, url);

            Page detailPage = null;
            try {
                detailPage = context.newPage();
                detailPage.setDefaultTimeout(DEFAULT_TIMEOUT);
                detailPage.navigate(url);
                try {
                    detailPage.waitForLoadState(LoadState.DOMCONTENTLOADED);
                } catch (Exception ignore) {
                }
                try {
                    detailPage.waitForLoadState(LoadState.NETWORKIDLE);
                } catch (Exception ignore) {
                }

                // 若出现遮罩类验证码，尝试自动处理（仅图片验证码 OCR）
                trySolveCaptcha(detailPage, options);

                String title = safePageTitle(url, detailPage);
                try {
                    saveDetailHtml(detailPage, title);
                } catch (Exception e) {
                    logger.debug("保存详情HTML失败: {}", e.getMessage());
                }

                // 使用现成下载逻辑（包含下载验证码自动识别）
                downloadAttachments(detailPage, title, options);
            } catch (Exception e) {
                logger.error("处理详情页失败: {} -> {}", url, e.getMessage());
            } finally {
                if (detailPage != null) {
                    try {
                        detailPage.close();
                    } catch (Exception ignore) {
                    }
                }
            }
        }
    }

    private String safePageTitle(String fallback, Page page) {
        try {
            String t = page.title();
            if (t != null && !t.isBlank())
                return t.trim();
        } catch (Exception ignore) {
        }
        // 回退到URL末段
        try {
            if (fallback != null) {
                int q = fallback.indexOf('?');
                String path = (q >= 0) ? fallback.substring(0, q) : fallback;
                int slash = path.lastIndexOf('/');
                return (slash >= 0) ? path.substring(slash + 1) : path;
            }
        } catch (Exception ignore) {
        }
        return "unknown";
    }

    // 已移除：列表页搜索与筛选流程

    // 已移除人工等待验证码逻辑

    /**
     * 自动尝试解决验证码（仅支持数字/字母图片验证码）：
     * - 若存在图片验证码与输入框，调用 OCR 识别并提交
     * 返回是否已成功通过（遮罩消失）
     */
    private boolean trySolveCaptcha(Page page, Options options) {
        boolean hasMask = isElementVisible(page, "#mpanel4 .mask") || isElementVisible(page, ".verifybox");
        if (!hasMask)
            return true;

        logger.info("检测到验证码，尝试 OCR 自动处理...");

        // 2) 图片验证码 + 输入框（若存在）
        // 允许不传端点时使用 OcrClient 默认本地端点
        if ((options.ocrEndpoint != null && !options.ocrEndpoint.isBlank()) || true) {
            try {
                Locator imgPanel = page.locator("#mpanel4 .verify-img-panel, .verify-img-panel");
                Locator input = page.locator("#mpanel4 input[type='text'], .verifybox input[type='text']");
                if (imgPanel.count() > 0 && input.count() > 0) {
                    Path tmp = Files.createTempFile("yngp_captcha", ".png");
                    imgPanel.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                    String ocrText = (options.ocrEndpoint == null || options.ocrEndpoint.isBlank())
                            ? OcrClient.recognize(tmp)
                            : OcrClient.recognize(options.ocrEndpoint, tmp);
                    if (ocrText != null && !ocrText.isBlank()) {
                        String code = ocrText.replaceAll("[^a-zA-Z0-9]", "");
                        if (!code.isBlank()) {
                            input.first().fill(code);
                            Locator okBtn = page.locator(
                                    "#mpanel4 .verify-btn, .verifybox .verify-btn, #mpanel4 .verify-refresh + button");
                            if (okBtn.count() > 0) {
                                okBtn.first().click();
                            } else {
                                input.first().press("Enter");
                            }
                            page.waitForTimeout(1500);
                            boolean solved = !(isElementVisible(page, "#mpanel4 .mask")
                                    || isElementVisible(page, ".verifybox"));
                            if (solved) {
                                logger.info("图片验证码已通过");
                                try {
                                    Files.deleteIfExists(tmp);
                                } catch (IOException ignore) {
                                }
                                return true;
                            }
                        }
                    }
                    try {
                        Files.deleteIfExists(tmp);
                    } catch (IOException ignore) {
                    }
                }
            } catch (Exception e) {
                logger.info("OCR 验证路径失败: {}", e.getMessage());
            }
        }

        logger.info("OCR 自动处理验证码未成功");
        return false;
    }

    // OCR 调用逻辑已迁移至 OcrClient

    // 已移除：列表结果表格等待逻辑

    // 已移除：列表页解析与详情逐条处理逻辑

    // 已移除：从列表页打开详情页的逻辑

    private void downloadAttachments(Page page, String title, Options options) {
        Path downloadDir = getDownloadDir();
        try {
            Files.createDirectories(downloadDir);
            logger.info("下载目录: {}", downloadDir.toAbsolutePath());
        } catch (Exception e) {
            logger.error("创建下载目录失败: {}", e.getMessage());
            return;
        }

        // 全局兜底：注册下载/响应钩子，确保任何浏览器触发的下载都被保存并写入清单
        AtomicBoolean suppressHook = new AtomicBoolean(false);
        AtomicBoolean completed = new AtomicBoolean(false);
        Set<String> attemptedUrls = java.util.Collections.synchronizedSet(new HashSet<>());
        Set<String> savedByUrl = java.util.Collections.synchronizedSet(new HashSet<>());
        try {
            registerDownloadHooks(page, title, downloadDir, suppressHook, completed, savedByUrl);
        } catch (Exception ignore) {
        }

        int totalDownloaded = 0;

        // 1) 优先尝试当前页面内的直链下载（后缀命中）
        totalDownloaded += processPageDirectLinks(page, title, downloadDir, options, suppressHook, attemptedUrls,
                completed);

        // 2) 如果还没有，尝试基于文本的下载入口（按钮/链接文本）
        if (!completed.get() && totalDownloaded == 0) {
            totalDownloaded += processPageTextLinks(page, title, downloadDir, options, suppressHook, attemptedUrls,
                    completed);
        }

        // 3) 尝试 iframe 内部的相同策略
        if (!completed.get() && totalDownloaded == 0) {
            for (Frame f : page.frames()) {
                try {
                    if (f.page() != page) {
                        // 子页的 frame 也归属当前 page；直接用 page 等待下载并触发 frame 元素点击
                    }
                    totalDownloaded += processFrameDirectLinks(page, f, title, downloadDir, options, suppressHook,
                            attemptedUrls, completed);
                    if (!completed.get() && totalDownloaded == 0) {
                        totalDownloaded += processFrameTextLinks(page, f, title, downloadDir, options, suppressHook,
                                attemptedUrls, completed);
                    }
                    if (completed.get() || totalDownloaded > 0)
                        break;
                } catch (Exception ignore) {
                }
            }
        }

        if (totalDownloaded == 0) {
            logger.info("未能触发下载，可能需要登录或页面使用非常规方式提供附件。");
        } else {
            logger.info("成功下载 {} 个附件到: {}", totalDownloaded, downloadDir.toAbsolutePath());
        }
    }

    private int processPageDirectLinks(Page page, String title, Path downloadDir, Options options,
            AtomicBoolean suppressHook, Set<String> attemptedUrls, AtomicBoolean completed) {
        String selector = "a[href$='.pdf'], a[href$='.doc'], a[href$='.docx'], a[href$='.xls'], a[href$='.xlsx'], a[href$='.zip'], a[href$='.rar'], a[href*='download'], a[href*='filemanager'], a[href*='FileDown'], a[href*='fileDown']";
        return processLocatorSetWithDownload(page, page.locator(selector), title, downloadDir, options, suppressHook,
                attemptedUrls, completed);
    }

    private int processPageTextLinks(Page page, String title, Path downloadDir, Options options,
            AtomicBoolean suppressHook, Set<String> attemptedUrls, AtomicBoolean completed) {
        String[] texts = new String[] { "采购文件", "招标文件", "附件", "附件下载", "下载" };
        int sum = 0;
        for (String t : texts) {
            Locator loc = page.locator(
                    "a:has-text('" + t + "'), button:has-text('" + t + "'), [role=button]:has-text('" + t + "')");
            sum += processLocatorSetWithDownload(page, loc, title, downloadDir, options, suppressHook, attemptedUrls,
                    completed);
            if (completed.get() || sum > 0)
                break;
        }
        return sum;
    }

    private int processFrameDirectLinks(Page page, Frame frame, String title, Path downloadDir, Options options,
            AtomicBoolean suppressHook, Set<String> attemptedUrls, AtomicBoolean completed) {
        String selector = "a[href$='.pdf'], a[href$='.doc'], a[href$='.docx'], a[href$='.xls'], a[href$='.xlsx'], a[href$='.zip'], a[href$='.rar'], a[href*='download'], a[href*='filemanager'], a[href*='FileDown'], a[href*='fileDown']";
        return processLocatorSetWithDownload(page, frame.locator(selector), title, downloadDir, options, suppressHook,
                attemptedUrls, completed);
    }

    private int processFrameTextLinks(Page page, Frame frame, String title, Path downloadDir, Options options,
            AtomicBoolean suppressHook, Set<String> attemptedUrls, AtomicBoolean completed) {
        String[] texts = new String[] { "采购文件", "招标文件", "附件", "附件下载", "下载" };
        int sum = 0;
        for (String t : texts) {
            Locator loc = frame.locator(
                    "a:has-text('" + t + "'), button:has-text('" + t + "'), [role=button]:has-text('" + t + "')");
            sum += processLocatorSetWithDownload(page, loc, title, downloadDir, options, suppressHook, attemptedUrls,
                    completed);
            if (completed.get() || sum > 0)
                break;
        }
        return sum;
    }

    private int processLocatorSetWithDownload(Page page, Locator loc, String title, Path downloadDir, Options options,
            AtomicBoolean suppressHook, Set<String> attemptedUrls, AtomicBoolean completed) {
        int downloaded = 0;
        int count = 0;
        try {
            count = loc.count();
        } catch (Exception ignore) {
        }
        if (count == 0)
            return 0;

        Set<String> triedHrefs = new HashSet<>();
        for (int i = 0; i < count; i++) {
            Locator el = loc.nth(i);
            String href = null;
            try {
                href = el.getAttribute("href");
            } catch (Exception ignore) {
            }
            if (href != null) {
                if (triedHrefs.contains(href))
                    continue;
                triedHrefs.add(href);
                if (attemptedUrls != null && attemptedUrls.contains(href))
                    continue;
            }

            // 第一次尝试点击触发下载
            if (!completed.get() && attemptClickDownload(page, el, title, downloadDir, 8000, suppressHook)) {
                downloaded++;
                if (href != null && attemptedUrls != null)
                    attemptedUrls.add(href);
                completed.set(true);
                continue;
            }

            // 如弹出验证码，先自动识别提交，再重试下载
            try {
                if (!completed.get() && isDownloadCaptchaVisible(page)) {
                    boolean passed = trySolveDownloadCaptcha(page, options);
                    if (passed && attemptClickDownload(page, el, title, downloadDir, 8000, suppressHook)) {
                        downloaded++;
                        if (href != null && attemptedUrls != null)
                            attemptedUrls.add(href);
                        completed.set(true);
                        continue;
                    }
                }
            } catch (Exception ignore) {
            }

            // 处理可能的新开页
            if (completed.get())
                break;
            Page popup = null;
            try {
                popup = page.waitForPopup(new Page.WaitForPopupOptions().setTimeout(1500), () -> {
                    try {
                        el.click(new Locator.ClickOptions().setForce(true));
                    } catch (Exception ignore) {
                    }
                });
            } catch (Exception ignore) {
            }
            if (popup != null) {
                try {
                    popup.waitForLoadState(LoadState.DOMCONTENTLOADED);
                } catch (Exception ignore) {
                }
                // 弹窗页也注册钩子，兜底保存
                try {
                    Set<String> popupSavedByUrl = java.util.Collections.synchronizedSet(new HashSet<>());
                    registerDownloadHooks(popup, title, downloadDir, suppressHook, completed, popupSavedByUrl);
                } catch (Exception ignore) {
                }
                int fromPopup = processPageDirectLinks(popup, title, downloadDir, options, suppressHook, attemptedUrls,
                        completed);
                if (!completed.get() && fromPopup == 0) {
                    fromPopup = processPageTextLinks(popup, title, downloadDir, options, suppressHook, attemptedUrls,
                            completed);
                }
                // 弹窗页也可能先出现验证码，识别后再试一次
                if (!completed.get() && fromPopup == 0) {
                    try {
                        if (isDownloadCaptchaVisible(popup) && trySolveDownloadCaptcha(popup, options)) {
                            fromPopup = processPageDirectLinks(popup, title, downloadDir, options, suppressHook,
                                    attemptedUrls, completed);
                            if (!completed.get() && fromPopup == 0) {
                                fromPopup = processPageTextLinks(popup, title, downloadDir, options, suppressHook,
                                        attemptedUrls, completed);
                            }
                        }
                    } catch (Exception ignore) {
                    }
                }
                downloaded += fromPopup;
                try {
                    popup.close();
                } catch (Exception ignore) {
                }
                if (completed.get() || fromPopup > 0)
                    continue;
            }

            // HTTP 回退（仅当存在 href 时）
            if (!completed.get() && href != null) {
                try {
                    boolean ok = tryHttpDownload(page.context(), page, el, title, downloadDir);
                    if (ok) {
                        downloaded++;
                        if (href != null && attemptedUrls != null)
                            attemptedUrls.add(href);
                        completed.set(true);
                    }
                } catch (Exception ignore) {
                }
            }
            if (completed.get())
                break;
        }
        return downloaded;
    }

    private boolean attemptClickDownload(Page page, Locator el, String title, Path downloadDir, int timeoutMs,
            AtomicBoolean suppressHook) {
        try {
            try {
                el.scrollIntoViewIfNeeded();
            } catch (Exception ignore) {
            }
            try {
                el.evaluate("el => { try { if (el.target) el.target = '_self'; } catch(e) {} }");
            } catch (Exception ignore) {
            }

            // 优先从元素/页面解析预期文件名
            String preferredName = extractPreferredNameFromElement(el);
            if (preferredName == null || preferredName.isBlank()) {
                try {
                    preferredName = extractPreferredNameFromHtml(page.content());
                    if (preferredName != null) {
                        logger.info("从页面HTML提取到首选文件名: {}", preferredName);
                    }
                } catch (Exception ignore) {
                }
            }

            Download dl = page.waitForDownload(new Page.WaitForDownloadOptions().setTimeout(timeoutMs), () -> {
                try {
                    el.click(new Locator.ClickOptions().setForce(true));
                } catch (Exception e1) {
                    try {
                        el.evaluate("el => el.click()");
                    } catch (Exception e2) {
                        try {
                            el.dispatchEvent("click");
                        } catch (Exception ignore) {
                        }
                    }
                }
            });
            if (dl != null) {
                // 暂停钩子以避免重复保存
                boolean prev = false;
                if (suppressHook != null) {
                    prev = suppressHook.get();
                    suppressHook.set(true);
                }
                try {
                    return saveDownload(dl, title, downloadDir, preferredName);
                } finally {
                    if (suppressHook != null)
                        suppressHook.set(prev);
                }
            }
        } catch (Exception ignore) {
        }
        return false;
    }

    // 已移除：等待计数器递增的工具方法（未使用）

    /**
     * 若响应看起来是可下载附件，则将其 body 保存到目标目录。
     * 返回是否保存成功。
     */
    private boolean saveAttachmentFromResponse(Response response, String title, Path downloadDir,
            Set<String> savedByUrl) {
        try {
            if (!isAttachmentLikeResponse(response))
                return false;

            String url = response.url();
            if (url == null || url.isBlank())
                return false;

            // 避免重复保存
            if (savedByUrl.contains(url))
                return false;

            byte[] body = response.body();
            if (body == null || body.length == 0)
                return false;

            String fileName = extractFilename(response, url);
            if (fileName == null || fileName.isBlank()) {
                fileName = "unknown_file_" + System.currentTimeMillis();
            }

            String finalName = sanitize(title) + "_" + sanitize(fileName);
            Path savePath = downloadDir.resolve(finalName);

            // 若同名已存在，追加序号
            int seq = 1;
            while (Files.exists(savePath)) {
                String base = finalName;
                String ext = "";
                int dot = finalName.lastIndexOf('.');
                if (dot > 0 && dot < finalName.length() - 1) {
                    base = finalName.substring(0, dot);
                    ext = finalName.substring(dot);
                }
                savePath = downloadDir.resolve(base + "(" + (seq++) + ")" + ext);
            }

            Files.createDirectories(savePath.getParent());
            Files.write(savePath, body);
            long size = Files.size(savePath);
            if (size > 0) {
                savedByUrl.add(url);
                logger.info("已通过响应流保存附件: {} ({} bytes)", savePath.toAbsolutePath(), size);
                recordSavedFile(downloadDir, savePath);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 判断响应是否像一个附件下载
     */
    private boolean isAttachmentLikeResponse(Response response) {
        try {
            int status = response.status();
            if (status < 200 || status >= 400)
                return false;

            String url = response.url();
            String urlLower = url == null ? "" : url.toLowerCase();

            String ct = headerIgnoreCase(response, "content-type");
            String cd = headerIgnoreCase(response, "content-disposition");

            boolean dispositionAttachment = cd != null && cd.toLowerCase().contains("attachment");
            boolean dispositionHasFilename = cd != null && cd.toLowerCase().contains("filename");

            boolean urlLooksFile = urlLower.endsWith(".pdf") || urlLower.endsWith(".doc") || urlLower.endsWith(".docx")
                    ||
                    urlLower.endsWith(".xls") || urlLower.endsWith(".xlsx") || urlLower.endsWith(".zip")
                    || urlLower.endsWith(".rar");

            boolean contentTypeFile = ct != null && (ct.toLowerCase().startsWith("application/") ||
                    ct.toLowerCase().contains("pdf") ||
                    ct.toLowerCase().contains("msword") ||
                    ct.toLowerCase().contains("officedocument") ||
                    ct.toLowerCase().contains("zip") ||
                    ct.toLowerCase().contains("rar"));

            return dispositionAttachment || dispositionHasFilename || urlLooksFile || contentTypeFile;
        } catch (Exception e) {
            return false;
        }
    }

    private String headerIgnoreCase(Response response, String name) {
        try {
            if (response.headers() == null)
                return null;
            String v = response.headers().get(name);
            if (v != null)
                return v;
            // 常见大小写变体
            v = response.headers().get(name.toLowerCase());
            if (v != null)
                return v;
            v = response.headers().get(name.toUpperCase());
            return v;
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 从 Content-Disposition 或 URL 推断原始文件名
     */
    private String extractFilename(Response response, String url) {
        String cd = headerIgnoreCase(response, "content-disposition");
        if (cd != null) {
            try {
                String lower = cd.toLowerCase();
                // 处理 filename*=UTF-8''...
                int idxStar = lower.indexOf("filename*=");
                if (idxStar >= 0) {
                    String part = cd.substring(idxStar + 9).trim();
                    // 可能形如 UTF-8''%E4%B8%AD%E6%96%87.doc
                    String encoded = part.substring(Math.max(part.lastIndexOf("''") + 2, idxStar + 9)).trim();
                    encoded = encoded.replaceAll("^[\"]|[\"]$", "");
                    try {
                        return URLDecoder.decode(encoded, java.nio.charset.StandardCharsets.UTF_8);
                    } catch (Exception ignore) {
                    }
                }
                // 处理 filename="..." 或 filename=...
                int idx = lower.indexOf("filename=");
                if (idx >= 0) {
                    String part = cd.substring(idx + 9).trim();
                    if (part.startsWith("\"")) {
                        int end = part.indexOf("\"", 1);
                        if (end > 1)
                            return part.substring(1, end);
                    }
                    int sc = part.indexOf(';');
                    if (sc > 0)
                        part = part.substring(0, sc);
                    return part;
                }
            } catch (Exception ignore) {
            }
        }
        // 尝试从 URL 查询参数中解析 file_name/filename
        try {
            String qName = parseFilenameFromQuery(url);
            if (qName != null && !qName.isBlank())
                return qName;
        } catch (Exception ignore) {
        }
        try {
            // 从 URL 推断
            String path = url;
            int q = path.indexOf('?');
            if (q >= 0)
                path = path.substring(0, q);
            int slash = path.lastIndexOf('/');
            String name = (slash >= 0) ? path.substring(slash + 1) : path;
            return URLDecoder.decode(name, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 保存下载文件的统一方法
     */
    private boolean saveDownload(Download download, String title, Path downloadDir) {
        return saveDownload(download, title, downloadDir, null);
    }

    private boolean saveDownload(Download download, String title, Path downloadDir, String preferredName) {
        try {
            String suggestedName = download.suggestedFilename();
            if (suggestedName == null || suggestedName.isBlank()) {
                suggestedName = "unknown_file_" + System.currentTimeMillis();
                logger.warn("下载文件名为空，使用默认文件名: {}", suggestedName);
            }

            // 改进文件名提取逻辑
            String finalName = determineFinalFilename(download, suggestedName, preferredName);

            // 如果仍然是默认名称，尝试从当前页面HTML中提取
            if (finalName.startsWith("attachment_") || finalName.startsWith("unknown_file_")) {
                try {
                    // 这里需要访问当前页面，但Download对象没有直接的页面引用
                    // 作为改进，可以在调用saveDownload时传入页面HTML内容
                    logger.debug("使用默认文件名，可能需要从页面HTML中提取更好的名称");
                } catch (Exception ignore) {
                }
            }

            String fileName = sanitize(title) + "_" + sanitize(finalName);
            Path savePath = ensureUniquePath(downloadDir, fileName);

            logger.info("准备保存文件: {} -> {}", suggestedName, savePath.toAbsolutePath());

            // 确保父目录存在
            Files.createDirectories(savePath.getParent());

            // 保存文件
            download.saveAs(savePath);

            // 等待文件写入完成（最多等待5秒）
            for (int i = 0; i < 50; i++) {
                if (Files.exists(savePath)) {
                    try {
                        long fileSize = Files.size(savePath);
                        if (fileSize > 0) {
                            logger.info("文件保存成功: {}, 大小: {} bytes", savePath.toAbsolutePath(), fileSize);
                            recordSavedFile(downloadDir, savePath);
                            return true;
                        }
                    } catch (Exception ignore) {
                    }
                }
                try {
                    Thread.sleep(100);
                } catch (InterruptedException ignore) {
                }
            }

            // 如果等待后仍然没有文件，记录详细错误信息
            if (!Files.exists(savePath)) {
                logger.error("文件保存失败: 文件不存在 {}", savePath.toAbsolutePath());
                logger.error("下载目录内容: {}", listDirectoryContents(downloadDir));

                // 尝试使用download.path()获取实际下载位置
                try {
                    Path actualPath = download.path();
                    if (actualPath != null && Files.exists(actualPath)) {
                        logger.info("文件实际下载到: {}", actualPath.toAbsolutePath());
                        // 尝试移动到目标位置
                        // 再次确保重名处理
                        if (Files.exists(savePath)) {
                            savePath = ensureUniquePath(downloadDir, fileName);
                        }
                        Files.move(actualPath, savePath);
                        logger.info("文件已移动到目标位置: {}", savePath.toAbsolutePath());
                        recordSavedFile(downloadDir, savePath);
                        return true;
                    }
                } catch (Exception e) {
                    logger.debug("无法获取实际下载路径: {}", e.getMessage());
                }
                return false;
            } else {
                long fileSize = Files.size(savePath);
                logger.info("文件保存成功: {}, 大小: {} bytes", savePath.toAbsolutePath(), fileSize);
                recordSavedFile(downloadDir, savePath);
                return true;
            }

        } catch (Exception e) {
            logger.error("保存下载文件时出现异常: {}", e.getMessage(), e);
            return false;
        }
    }

    private void registerDownloadHooks(Page page, String title, Path downloadDir, AtomicBoolean suppressHook,
            AtomicBoolean completed, Set<String> savedByUrl) {
        try {
            page.onDownload(download -> {
                try {
                    if (suppressHook != null && suppressHook.get())
                        return;
                    if (completed != null && completed.get())
                        return;
                    if (saveDownload(download, title, downloadDir)) {
                        completed.set(true);
                    }
                } catch (Exception ignore) {
                }
            });
            page.onResponse(response -> {
                try {
                    if (suppressHook != null && suppressHook.get())
                        return;
                    if (completed != null && completed.get())
                        return;
                    if (saveAttachmentFromResponse(response, title, downloadDir, savedByUrl)) {
                        completed.set(true);
                    }
                } catch (Exception ignore) {
                }
            });
        } catch (Exception e) {
            logger.debug("注册下载监听失败: {}", e.getMessage());
        }
    }

    private Path ensureUniquePath(Path dir, String finalName) {
        Path savePath = dir.resolve(finalName);
        int seq = 1;
        while (Files.exists(savePath)) {
            String base = finalName;
            String ext = "";
            int dot = finalName.lastIndexOf('.');
            if (dot > 0 && dot < finalName.length() - 1) {
                base = finalName.substring(0, dot);
                ext = finalName.substring(dot);
            }
            savePath = dir.resolve(base + "(" + (seq++) + ")" + ext);
        }
        return savePath;
    }

    private void recordSavedFile(Path downloadDir, Path savedPath) {
        try {
            Path index = downloadDir.resolve("_saved_files.txt");
            Files.createDirectories(downloadDir);
            String line = java.time.LocalDateTime.now() + "\t" + savedPath.toAbsolutePath() + System.lineSeparator();
            Files.write(index, line.getBytes(java.nio.charset.StandardCharsets.UTF_8),
                    java.nio.file.StandardOpenOption.CREATE, java.nio.file.StandardOpenOption.APPEND);
        } catch (Exception ignore) {
        }
    }

    /**
     * 列出目录内容，用于调试
     */
    private String listDirectoryContents(Path dir) {
        try {
            if (!Files.exists(dir))
                return "目录不存在";
            return Files.list(dir)
                    .map(p -> p.getFileName().toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("目录为空");
        } catch (Exception e) {
            return "无法读取目录: " + e.getMessage();
        }
    }

    private boolean isDownloadCaptchaVisible(Page page) {
        try {
            String[] sels = new String[] {
                    ".layui-layer:visible", ".bootstrap-dialog:visible", ".verifybox:visible", "#mpanel4 .mask"
            };
            for (String s : sels) {
                if (isElementVisible(page, s))
                    return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    // 已移除：HTTP GET 字节工具方法（未使用）

    private String ocrFromCaptchaElement(Page page, Locator img, Locator panel, String endpoint) {
        try {
            // 1) 优先从 <img src> 直取
            if (img != null && img.count() > 0) {
                String src = null;
                try {
                    src = img.first().getAttribute("src");
                } catch (Exception ignore) {
                }
                if (src != null && !src.isBlank()) {
                    // data:image 或 http(s)
                    if (src.startsWith("data:image/")) {
                        int comma = src.indexOf(",");
                        if (comma > 0) {
                            String b64 = src.substring(comma + 1);
                            byte[] bytes = java.util.Base64.getDecoder().decode(b64);
                            return OcrClient.recognizeBytes(endpoint, bytes, "captcha.png", 3000, 6000, 6000);
                        }
                    } else {
                        // 为确保与当前会话一致，优先元素截图而不是重新 HTTP 拉取
                        Path tmp = Files.createTempFile("yngp_dl_captcha", ".png");
                        try {
                            img.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                            return (endpoint == null) ? OcrClient.recognize(tmp) : OcrClient.recognize(endpoint, tmp);
                        } finally {
                            try {
                                Files.deleteIfExists(tmp);
                            } catch (Exception ignore) {
                            }
                        }
                        // 直连方案已关闭，避免与当前可见验证码不一致
                    }
                }
            }
            // 2) 回退到元素截图
            Path tmp = Files.createTempFile("yngp_dl_captcha", ".png");
            try {
                if (img != null && img.count() > 0) {
                    img.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                } else if (panel != null && panel.count() > 0) {
                    panel.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                } else {
                    page.screenshot(new Page.ScreenshotOptions().setPath(tmp));
                }
                return (endpoint == null) ? OcrClient.recognize(tmp) : OcrClient.recognize(endpoint, tmp);
            } finally {
                try {
                    Files.deleteIfExists(tmp);
                } catch (Exception ignore) {
                }
            }
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 下载弹出验证码时尝试自动识别并提交。
     * 返回是否疑似已通过（根据遮罩消失/对话框关闭来判断）。
     */
    private boolean trySolveDownloadCaptcha(Page page, Options options) {
        try {
            // 可能的验证码容器与图片、输入框选择器
            Locator panel = page.locator(
                    ".layui-layer .layui-layer-content, .bootstrap-dialog .modal-body, .verifybox, #mpanel4, .modal:visible");
            if (panel.count() == 0)
                return false;

            Locator img = page.locator(
                    "#imgObj, #mpanel4 .verify-img-panel img, .verify-img-panel img, img#kaptchaImage, img#captchaimg, img.kaptcha-img, img.captcha, img[alt*=验证码], img[src*='captcha'], img[src*='kaptcha']");
            Locator input = page.locator(
                    "#veryCode, #mpanel4 input[type='text'], .verifybox input[type='text'], input[placeholder*='验证码'], input[name*='captcha'], input[name*='kaptcha']");
            Locator okBtn = page.locator(
                    "#doSubmit, .verify-btn, button:has-text('确认'), button:has-text('确定'), button:has-text('提交'), .layui-layer-btn .layui-layer-btn0, .bootstrap-dialog .btn-primary");

            // 若主文档未找到，尝试在 iframe 中查找
            if (img.count() == 0 || input.count() == 0) {
                for (Frame f : page.frames()) {
                    try {
                        Locator fImg = f.locator(
                                "#imgObj, #mpanel4 .verify-img-panel img, .verify-img-panel img, img#kaptchaImage, img#captchaimg, img.kaptcha-img, img.captcha, img[alt*=验证码], img[src*='captcha'], img[src*='kaptcha']");
                        Locator fInput = f.locator(
                                "#veryCode, #mpanel4 input[type='text'], .verifybox input[type='text'], input[placeholder*='验证码'], input[name*='captcha'], input[name*='kaptcha']");
                        if (fImg.count() > 0 && fInput.count() > 0) {
                            img = fImg;
                            input = fInput;
                            okBtn = f.locator(
                                    "#doSubmit, .verify-btn, button:has-text('确认'), button:has-text('确定'), button:has-text('提交'), .layui-layer-btn .layui-layer-btn0, .bootstrap-dialog .btn-primary");
                            break;
                        }
                    } catch (Exception ignore) {
                    }
                }
            }

            if (input.count() == 0)
                return false;

            // 仅识别当前可见图片，不做刷新，避免获取到“新验证码”
            int attempts = 1;
            for (int round = 0; round < attempts; round++) {
                String endpoint = (options != null && options.ocrEndpoint != null && !options.ocrEndpoint.isBlank())
                        ? options.ocrEndpoint
                        : null;
                String ocrText = ocrFromCaptchaElement(page, img, panel, endpoint);

                if (ocrText == null || ocrText.isBlank())
                    continue;
                String code = ocrText.replaceAll("[^a-zA-Z0-9]", "");
                if (code.isBlank())
                    continue;

                logger.info("下载验证码 OCR 识别: '{}'", code);
                input.first().fill(code);
                if (okBtn.count() > 0)
                    okBtn.first().click();
                else
                    input.first().press("Enter");

                page.waitForTimeout(600);
                boolean stillVisible = isElementVisible(page,
                        ".layui-layer:visible, .bootstrap-dialog:visible, .verifybox:visible, #mpanel4 .mask");
                if (!stillVisible)
                    return true;
            }
            return false;
        } catch (Exception e) {
            logger.debug("下载验证码自动识别失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 直接解析元素 href，并用当前上下文 Cookie 通过 OkHttp 发起 GET 保存到本地。
     */
    private boolean tryHttpDownload(BrowserContext context, Page page, Locator anchor, String title, Path downloadDir) {
        try {
            String href = null;
            try {
                href = anchor.getAttribute("href");
            } catch (Exception ignore) {
            }
            if (href == null || href.isBlank())
                return false;

            String absUrl = toAbsoluteUrl(page.url(), href);
            if (absUrl == null)
                return false;

            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            okhttp3.Request.Builder rb = new okhttp3.Request.Builder().url(absUrl).get()
                    .addHeader("User-Agent",
                            "Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome Safari")
                    .addHeader("Accept", "*/*");

            // 注入 Cookie（当前域）
            String cookieHeader = buildCookieHeader(context, absUrl);
            if (cookieHeader != null) {
                rb.addHeader("Cookie", cookieHeader);
            }

            try (okhttp3.Response resp = client.newCall(rb.build()).execute()) {
                if (!resp.isSuccessful() || resp.body() == null)
                    return false;

                // 预先从元素/URL推断文件名
                String preferred = extractPreferredNameFromElement(anchor);
                String fname = null;
                String cd = resp.header("Content-Disposition");
                if (cd != null && cd.toLowerCase().contains("filename")) {
                    fname = parseFilenameFromContentDisposition(cd);
                }
                if ((fname == null || fname.isBlank()) && preferred != null && !preferred.isBlank()) {
                    fname = preferred;
                }
                if (fname == null || fname.isBlank()) {
                    String fromQuery = parseFilenameFromQuery(absUrl);
                    if (fromQuery != null && !fromQuery.isBlank())
                        fname = fromQuery;
                }
                if (fname == null || fname.isBlank()) {
                    fname = extractNameFromUrl(absUrl);
                }
                if (fname == null || fname.isBlank()) {
                    fname = "unknown_file_" + System.currentTimeMillis();
                }

                String finalName = sanitize(title) + "_" + sanitize(fname);
                Path savePath = downloadDir.resolve(finalName);
                int seq = 1;
                while (Files.exists(savePath)) {
                    String base = finalName;
                    String ext = "";
                    int dot = finalName.lastIndexOf('.');
                    if (dot > 0 && dot < finalName.length() - 1) {
                        base = finalName.substring(0, dot);
                        ext = finalName.substring(dot);
                    }
                    savePath = downloadDir.resolve(base + "(" + (seq++) + ")" + ext);
                }

                byte[] bytes = resp.body().bytes();
                if (bytes == null || bytes.length == 0)
                    return false;
                Files.createDirectories(savePath.getParent());
                Files.write(savePath, bytes);
                long size = Files.size(savePath);
                if (size > 0) {
                    logger.info("已通过HTTP保存附件: {} ({} bytes)", savePath.toAbsolutePath(), size);
                    // 记录保存清单
                    recordSavedFile(downloadDir, savePath);
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private String parseFilenameFromQuery(String url) {
        try {
            java.net.URI uri = java.net.URI.create(url);
            String query = uri.getRawQuery();
            if (query == null || query.isBlank())
                return null;
            String[] parts = query.split("&");
            for (String p : parts) {
                int eq = p.indexOf('=');
                String key = eq >= 0 ? p.substring(0, eq) : p;
                String val = eq >= 0 ? p.substring(eq + 1) : "";
                key = java.net.URLDecoder.decode(key, java.nio.charset.StandardCharsets.UTF_8);
                val = java.net.URLDecoder.decode(val, java.nio.charset.StandardCharsets.UTF_8);
                if ("file_name".equalsIgnoreCase(key) || "filename".equalsIgnoreCase(key)) {
                    return val;
                }
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    private String extractPreferredNameFromElement(Locator el) {
        try {
            // 1) javascript:download('id','/path','file.ext')
            String href = null;
            try {
                href = el.getAttribute("href");
            } catch (Exception ignore) {
            }
            String onclick = null;
            try {
                onclick = el.getAttribute("onclick");
            } catch (Exception ignore) {
            }
            String s = (href != null && href.startsWith("javascript:")) ? href : onclick;
            if (s != null && s.toLowerCase().contains("download")) {
                int l = s.indexOf('(');
                int r = s.lastIndexOf(')');
                if (l >= 0 && r > l) {
                    String inside = s.substring(l + 1, r);
                    String[] args = inside.split(",");
                    if (args.length >= 3) {
                        String third = args[2].trim();
                        if ((third.startsWith("\"") && third.endsWith("\""))
                                || (third.startsWith("'") && third.endsWith("'"))) {
                            third = third.substring(1, third.length() - 1);
                        }
                        if (!third.isBlank())
                            return third;
                    }
                }
            }
            // 2) filemanager.do?...&file_name=...
            if (href != null && href.contains("filemanager.do")) {
                String name = parseFilenameFromQuery(toAbsoluteUrl("http://dummy/", href));
                if (name != null && !name.isBlank())
                    return name;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    private String parseFilenameFromContentDisposition(String cd) {
        try {
            String lower = cd.toLowerCase();
            int idxStar = lower.indexOf("filename*=");
            if (idxStar >= 0) {
                String part = cd.substring(idxStar + 9).trim();
                String encoded = part.substring(Math.max(part.lastIndexOf("''") + 2, idxStar + 9)).trim();
                encoded = encoded.replaceAll("^[\"]|[\"]$", "");
                try {
                    return URLDecoder.decode(encoded, java.nio.charset.StandardCharsets.UTF_8);
                } catch (Exception ignore) {
                }
            }
            int idx = lower.indexOf("filename=");
            if (idx >= 0) {
                String part = cd.substring(idx + 9).trim();
                if (part.startsWith("\"")) {
                    int end = part.indexOf("\"", 1);
                    if (end > 1)
                        return part.substring(1, end);
                }
                int sc = part.indexOf(';');
                if (sc > 0)
                    part = part.substring(0, sc);
                return part;
            }
        } catch (Exception ignore) {
        }
        return null;
    }

    private String extractNameFromUrl(String url) {
        try {
            String path = url;
            int q = path.indexOf('?');
            if (q >= 0)
                path = path.substring(0, q);
            int slash = path.lastIndexOf('/');
            String name = (slash >= 0) ? path.substring(slash + 1) : path;
            return URLDecoder.decode(name, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
    }

    private String buildCookieHeader(BrowserContext context, String absUrl) {
        try {
            List<Cookie> cookies = context.cookies(absUrl);
            if (cookies == null || cookies.isEmpty())
                return null;
            StringBuilder sb = new StringBuilder();
            for (Cookie c : cookies) {
                if (sb.length() > 0)
                    sb.append("; ");
                sb.append(c.name).append("=").append(c.value);
            }
            return sb.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private String toAbsoluteUrl(String base, String href) {
        try {
            if (href.startsWith("http://") || href.startsWith("https://"))
                return href;
            java.net.URI baseUri = java.net.URI.create(base);
            java.net.URI res = baseUri.resolve(href);
            return res.toString();
        } catch (Exception e) {
            return null;
        }
    }

    // 从当前页面 HTML 的附件表格提取第一个文件名，作为兜底的首选命名
    private String extractPreferredNameFromHtml(String html) {
        try {
            if (html == null || html.isBlank())
                return null;
            org.jsoup.nodes.Document doc = org.jsoup.Jsoup.parse(html);

            // 尝试多种可能的表格选择器
            org.jsoup.nodes.Element table = doc.selectFirst("table#filelist");
            if (table == null) {
                table = doc.selectFirst("table.filelist");
            }
            if (table == null) {
                table = doc.selectFirst("table[id*='file']");
            }
            if (table == null) {
                table = doc.selectFirst("table[class*='file']");
            }

            if (table != null) {
                org.jsoup.select.Elements rows = table.select("tbody > tr, tr");
                for (org.jsoup.nodes.Element tr : rows) {
                    org.jsoup.select.Elements tds = tr.select("td");
                    if (tds.size() >= 2) {
                        // 尝试不同的列位置查找文件名
                        for (int i = 0; i < tds.size(); i++) {
                            String text = tds.get(i).text();
                            if (text != null && !text.isBlank()) {
                                text = text.trim();
                                // 检查是否看起来像文件名
                                if (hasValidExtension(text) || text.contains(".")) {
                                    logger.info("从HTML表格提取文件名: {}", text);
                                    return text;
                                }
                            }
                        }
                    }
                }
            }

            // 如果表格方法失败，尝试查找页面中的其他文件名线索
            org.jsoup.select.Elements links = doc
                    .select("a[href*='download'], a[href*='filemanager'], a[href*='file']");
            for (org.jsoup.nodes.Element link : links) {
                String href = link.attr("href");
                String text = link.text();

                // 从链接文本中查找文件名
                if (text != null && !text.isBlank() && hasValidExtension(text.trim())) {
                    logger.info("从链接文本提取文件名: {}", text.trim());
                    return text.trim();
                }

                // 从href中查找文件名参数
                if (href != null && href.contains("file_name=")) {
                    String fileName = parseFilenameFromQuery(href);
                    if (fileName != null && !fileName.isBlank()) {
                        logger.info("从链接href提取文件名: {}", fileName);
                        return fileName;
                    }
                }
            }

            return null;
        } catch (Exception ignore) {
            return null;
        }
    }

    // 参考下载采集器逻辑：识别看起来像验证码的图片响应
    // 已移除：识别响应是否为验证码图片的工具方法（未使用）

    private void saveDetailHtml(Page page, String title) throws Exception {
        String html = page.content();
        Path dir = getRawHtmlDir();
        Files.createDirectories(dir);
        String unique = shortHashHex(page.url());
        Path file = dir.resolve(sanitize(title) + "_" + unique + ".html");
        try (FileWriter writer = new FileWriter(file.toFile())) {
            writer.write("<!-- URL: " + page.url() + " -->\n");
            writer.write(html);
        }
        logger.info("详情页HTML已保存: {}", file);
    }

    private boolean isElementVisible(Page page, String selector) {
        try {
            return page.isVisible(selector);
        } catch (Exception e) {
            return false;
        }
    }

    // 已移除：页面输入设置工具方法（未使用）

    // 已移除：获取元素文本工具方法（未使用）

    private Path getBaseDir() {
        if (this.resolvedBaseDir != null) {
            return this.resolvedBaseDir;
        }
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        return Paths.get("data", "yngp", today);
    }

    private Path getRawHtmlDir() {
        return getBaseDir().resolve("raw_html");
    }

    private Path getDownloadDir() {
        return getBaseDir().resolve("files");
    }

    private String sanitize(String name) {
        if (name == null)
            return "unknown";
        String n = name.replaceAll("[\\\\/:*?\"<>|]", "_").trim();
        if (n.isEmpty())
            return "unknown";
        return n.length() > 80 ? n.substring(0, 80) : n;
    }

    private String shortHashHex(String input) {
        try {
            if (input == null)
                input = String.valueOf(System.nanoTime());
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(input.getBytes(java.nio.charset.StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < 4 && i < digest.length; i++) {
                sb.append(String.format("%02x", digest[i]));
            }
            return sb.toString();
        } catch (Exception e) {
            return Long.toHexString(System.nanoTime());
        }
    }

    private Path resolveBaseDir(Options options) {
        if (options != null && options.outputDir != null && !options.outputDir.isBlank()) {
            Path p = Paths.get(options.outputDir);
            return p.isAbsolute() ? p : p.toAbsolutePath();
        }
        String today = LocalDate.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
        return Paths.get("data", "yngp", today).toAbsolutePath();
    }

    private Path resolvedBaseDir;

    /**
     * 改进的文件名确定逻辑
     */
    private String determineFinalFilename(Download download, String suggestedName, String preferredName) {
        try {
            // 1. 优先使用预设的首选名称
            if (preferredName != null && !preferredName.isBlank()) {
                String cleaned = preferredName.trim();
                if (hasValidExtension(cleaned)) {
                    logger.info("使用预设首选文件名: {}", cleaned);
                    return cleaned;
                }
            }

            // 2. 尝试从下载URL中提取更好的文件名
            String downloadUrl = null;
            try {
                downloadUrl = download.url();
                logger.debug("下载URL: {}", downloadUrl);
            } catch (Exception ignore) {
            }

            if (downloadUrl != null) {
                // 从URL查询参数中提取文件名
                String fromQuery = parseFilenameFromQuery(downloadUrl);
                if (fromQuery != null && !fromQuery.isBlank() && hasValidExtension(fromQuery)) {
                    logger.info("从URL查询参数提取文件名: {}", fromQuery);
                    return fromQuery;
                }

                // 从URL路径中提取文件名（排除.do等动态文件）
                String fromPath = extractNameFromUrl(downloadUrl);
                if (fromPath != null && !fromPath.isBlank() && hasValidExtension(fromPath)
                        && !fromPath.endsWith(".do")) {
                    logger.info("从URL路径提取文件名: {}", fromPath);
                    return fromPath;
                }
            }

            // 3. 检查建议的文件名是否有效
            if (suggestedName != null && !suggestedName.isBlank()) {
                String cleaned = suggestedName.trim();
                if (hasValidExtension(cleaned) && !cleaned.endsWith(".do")) {
                    logger.info("使用建议的文件名: {}", cleaned);
                    return cleaned;
                }
                logger.debug("建议的文件名无效或为动态文件: {}", cleaned);
            }

            // 4. 尝试从响应头中获取文件名（如果可能）
            // 注意：Playwright的Download对象可能不直接暴露响应头，这里作为备用逻辑

            // 5. 最后的回退：生成带扩展名的默认文件名
            String timestamp = String.valueOf(System.currentTimeMillis());
            String defaultName = "attachment_" + timestamp + ".pdf"; // 默认假设是PDF，因为政府采购文件通常是PDF
            logger.warn("无法确定文件名，使用默认名称: {}", defaultName);
            return defaultName;

        } catch (Exception e) {
            logger.debug("确定文件名时出错: {}", e.getMessage());
            String fallbackName = "unknown_file_" + System.currentTimeMillis() + ".pdf";
            logger.warn("文件名确定过程出错，使用回退名称: {}", fallbackName);
            return fallbackName;
        }
    }

    /**
     * 检查文件名是否有有效的扩展名
     */
    private boolean hasValidExtension(String filename) {
        if (filename == null || filename.isBlank())
            return false;
        String lower = filename.toLowerCase();
        return lower.endsWith(".pdf") || lower.endsWith(".doc") || lower.endsWith(".docx") ||
                lower.endsWith(".xls") || lower.endsWith(".xlsx") || lower.endsWith(".zip") ||
                lower.endsWith(".rar") || lower.endsWith(".txt") || lower.endsWith(".png") ||
                lower.endsWith(".jpg") || lower.endsWith(".jpeg");
    }

    private Path ensureUniquePath(Path dir, String finalName) {
        Path savePath = dir.resolve(finalName);
        int seq = 1;
        while (Files.exists(savePath)) {
            String base = finalName;
            String ext = "";
            int dot = finalName.lastIndexOf('.');
            if (dot > 0 && dot < finalName.length() - 1) {
                base = finalName.substring(0, dot);
                ext = finalName.substring(dot);
            }
            savePath = dir.resolve(base + "(" + (seq++) + ")" + ext);
        }
        return savePath;
    }

    private void recordSavedFile(Path downloadDir, Path savedPath) {
        try {
            Path index = downloadDir.resolve("_saved_files.txt");
            Files.createDirectories(downloadDir);
            String line = java.time.LocalDateTime.now() + "\t" + savedPath.toAbsolutePath() + System.lineSeparator();
            Files.write(index, line.getBytes(java.nio.charset.StandardCharsets.UTF_8),
                    java.nio.file.StandardOpenOption.CREATE, java.nio.file.StandardOpenOption.APPEND);
        } catch (Exception ignore) {
        }
    }

    /**
     * 列出目录内容，用于调试
     */
    private String listDirectoryContents(Path dir) {
        try {
            if (!Files.exists(dir))
                return "目录不存在";
            return Files.list(dir)
                    .map(p -> p.getFileName().toString())
                    .reduce((a, b) -> a + ", " + b)
                    .orElse("目录为空");
        } catch (Exception e) {
            return "无法读取目录: " + e.getMessage();
        }
    }

    public static void main(String[] args) {
        Options options = new Options();
        new YngpCrawler_old().run(options);
    }

}
