package org.hujun.crawler.websites.yngp.crawler;

import com.microsoft.playwright.*;
import com.microsoft.playwright.options.LoadState;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 云南省政府采购网 详情页附件下载爬虫。
 *
 * 职责：
 * - 从指定的链接文件读取详情页链接
 * - 打开详情页，解析“附件列表”
 * - 逐个点击下载；若触发验证码，截取验证码图片交给 {@link OcrClient} 识别并提交
 * - 将下载文件保存到以该页面“采购项目名称”命名的目录下，文件名以页面显示为准
 */
public class YngpCrawler {

    private static final Logger logger = LoggerFactory.getLogger(YngpCrawler.class);

    private final boolean headless;
    private final String linksFilePath; // 绝对路径
    private final String ocrEndpoint;   // 可为 null，使用默认端点
    public YngpCrawler(String linksFilePath, boolean headless) {
        this(linksFilePath, headless, null);
    }

    public YngpCrawler(String linksFilePath, boolean headless, String ocrEndpoint) {
        this.linksFilePath = Objects.requireNonNull(linksFilePath, "linksFilePath cannot be null");
        this.headless = headless;
        this.ocrEndpoint = ocrEndpoint;
    }

    public void start() {
        logger.info("启动 YngpCrawler，headless={}, linksFile={}", headless, linksFilePath);
        Path linksFile = Paths.get(linksFilePath);
        if (!Files.exists(linksFile)) {
            logger.error("链接文件不存在: {}", linksFile.toAbsolutePath());
            return;
        }

        List<String> urls = readLinesTrimmed(linksFile);
        logger.info("读取到 {} 条链接", urls.size());
        if (urls.isEmpty()) return;

        String baseOutputDir = linksFile.getParent() != null
                ? linksFile.getParent().toAbsolutePath().toString()
                : Paths.get("data", "yngp", LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")), "files").toAbsolutePath().toString();
        logger.info("下载输出基目录: {}", baseOutputDir);

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        try {
            playwright = Playwright.create();
            BrowserType.LaunchOptions launch = new BrowserType.LaunchOptions().setHeadless(headless).setTimeout(120_000);
            browser = playwright.chromium().launch(launch);
            context = browser.newContext(new Browser.NewContextOptions()
                    .setViewportSize(1366, 900)
                    .setAcceptDownloads(true));

            for (String url : urls) {
                if (url == null || url.isBlank()) continue;
                try {
                    processOneDetailPage(context, url.trim(), baseOutputDir);
                } catch (Exception e) {
                    logger.error("处理详情页失败: {} -> {}", url, e.getMessage(), e);
                }
            }
        } finally {
            if (context != null) try { context.close(); } catch (Exception ignore) {}
            if (browser != null) try { browser.close(); } catch (Exception ignore) {}
            if (playwright != null) try { playwright.close(); } catch (Exception ignore) {}
        }
    }

    private void processOneDetailPage(BrowserContext context, String url, String baseOutputDir) throws IOException {
        logger.info("打开详情页: {}", url);
        Page page = context.newPage();
        try {
            page.setDefaultTimeout(60_000);
            page.navigate(url);
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            page.waitForLoadState(LoadState.NETWORKIDLE);

            String html = page.content();
            String projectName = extractProjectName(html);
            if (projectName == null || projectName.isBlank()) {
                projectName = extractTitleFallback(html);
            }
            if (projectName == null || projectName.isBlank()) {
                projectName = "未命名项目";
            }
            String projectDirName = sanitizeFileName(projectName);
            Path targetDir = Paths.get(baseOutputDir, projectDirName);
            Files.createDirectories(targetDir);
            logger.info("当前页面项目目录: {}", targetDir.toAbsolutePath());

            List<AttachmentRow> attachments = findAttachments(page);
            logger.info("发现附件 {} 个", attachments.size());

            for (int i = 0; i < attachments.size(); i++) {
                AttachmentRow row = attachments.get(i);
                try {
                    downloadOneAttachment(page, row, targetDir);
                } catch (Exception e) {
                    logger.error("下载附件失败: {} -> {}", row.displayFileName, e.getMessage(), e);
                }
            }
        } finally {
            try { page.close(); } catch (Exception ignore) {}
        }
    }

    private List<String> readLinesTrimmed(Path file) {
        List<String> lines = new ArrayList<>();
        try (BufferedReader br = Files.newBufferedReader(file, StandardCharsets.UTF_8)) {
            String line;
            while ((line = br.readLine()) != null) {
                String s = line.trim();
                if (!s.isEmpty() && !s.startsWith("#")) {
                    lines.add(s);
                }
            }
        } catch (IOException e) {
            logger.error("读取链接文件失败: {}", e.getMessage(), e);
        }
        return lines;
    }

    private static String extractProjectName(String html) {
        try {
            Document doc = Jsoup.parse(html);
            // 优先从“公告概要”表格中读取“采购项目名称”
            Elements titleTds = doc.select("td.title:matchesOwn(\\s*采购项目名称\\s*)");
            if (!titleTds.isEmpty()) {
                Element titleTd = titleTds.first();
                if (titleTd != null) {
                    Element valueTd = null;
                    // 取兄弟中下一个非 title 的 td
                    Elements siblings = titleTd.parent() != null ? titleTd.parent().children() : new Elements();
                    int idx = -1;
                    for (int i = 0; i < siblings.size(); i++) {
                        if (siblings.get(i) == titleTd) { idx = i; break; }
                    }
                    if (idx >= 0) {
                        // 往后找第一个 td
                        for (int j = idx + 1; j < siblings.size(); j++) {
                            Element e = siblings.get(j);
                            if (e != null && e.tagName().equalsIgnoreCase("td")) { valueTd = e; break; }
                        }
                    }
                    if (valueTd == null) {
                        valueTd = titleTd.nextElementSibling();
                    }
                    if (valueTd != null) {
                        String text = valueTd.text();
                        if (text != null && !text.isBlank()) return text.trim();
                    }
                }
            }
        } catch (Exception ignore) {}
        return null;
    }

    private static String extractTitleFallback(String html) {
        try {
            Document doc = Jsoup.parse(html);
            Element h2 = doc.selectFirst(".vF_detail_header h2, h2");
            if (h2 != null) return h2.text();
        } catch (Exception ignore) {}
        return null;
    }

    private static String sanitizeFileName(String name) {
        String s = name.replaceAll("[\\\\/:*?\"<>|]", " ")
                .replaceAll("\\s+", " ")
                .trim();
        if (s.length() > 200) s = s.substring(0, 200);
        return s.isEmpty() ? "unnamed" : s;
    }

    private List<AttachmentRow> findAttachments(Page page) {
        List<AttachmentRow> list = new ArrayList<>();
        Locator rows = page.locator("#filelist tbody tr");
        int count = 0;
        try { count = rows.count(); } catch (Exception ignore) {}
        for (int i = 0; i < count; i++) {
            Locator row = rows.nth(i);
            String fileName = safeInnerText(row.locator("td").nth(2)); // 第3列通常为“文件名称”
            if (fileName == null || fileName.isBlank()) {
                fileName = safeInnerText(row.locator("td:nth-child(3)"));
            }
            // 记录行索引，后续点击时重新定位，避免旧句柄失效
            list.add(new AttachmentRow(i, fileName == null ? ("attachment-" + i) : fileName.trim()));
        }
        return list;
    }

    private static String safeInnerText(Locator locator) {
        try { return locator.innerText().trim(); } catch (Exception e) { return null; }
    }

    private void downloadOneAttachment(Page page, AttachmentRow row, Path targetDir) throws IOException {
        // 为本次点击注册下载与响应钩子（老代码路径）
        final java.util.concurrent.atomic.AtomicBoolean suppressHook = new java.util.concurrent.atomic.AtomicBoolean(false);
        final java.util.concurrent.atomic.AtomicBoolean completed = new java.util.concurrent.atomic.AtomicBoolean(false);
        final java.util.Set<String> savedByUrl = java.util.Collections.synchronizedSet(new java.util.HashSet<>());
        registerDownloadHooks(page, targetDir, suppressHook, completed, savedByUrl);

        Locator rows = page.locator("#filelist tbody tr");
        if (row.index < 0 || row.index >= rows.count()) return;
        Locator tr = rows.nth(row.index);

        // 优先通过“下载”文本定位
        Locator primaryLocator = tr.locator("a:has-text(\"下载\")");
        if (primaryLocator.count() == 0) {
            primaryLocator = tr.locator("a[href^=\"javascript:download\"], [onclick*=\"download(\"]");
        }
        if (primaryLocator.count() == 0) {
            logger.warn("未找到下载链接: {} (row {})", row.displayFileName, row.index);
            return;
        }

        logger.info("准备下载附件: {}", row.displayFileName);
        logger.info("点击下载...");

        // 先点击
        final Locator finalPrimary = primaryLocator;
        try { finalPrimary.first().click(); } catch (Exception e) { logger.warn("点击下载失败: {}", e.getMessage()); }

        // 直接使用老代码的验证码判定与处理逻辑
        if (isDownloadCaptchaVisible(page)) {
            logger.info("检测到验证码弹窗（老代码路径），开始处理...");
            boolean passed = trySolveDownloadCaptchaOld(page);
            if (!passed) {
                logger.warn("验证码处理失败或未通过(老代码路径): {}", row.displayFileName);
                return;
            }
            logger.info("[captcha] 已通过，重新点击下载触发文件保存(老代码路径)...");
            Download dl2 = tryWaitForDownload(page, () -> finalPrimary.first().click(), 12_000);
            if (dl2 != null) {
                saveDownload(dl2, targetDir, row.displayFileName);
            } else {
                logger.warn("[captcha] 通过后未捕获下载事件(老代码路径)，尝试HTTP回退...");
                try { if (tryHttpDownload(page.context(), page, finalPrimary.first(), targetDir)) return; } catch (Exception ignore) {}
            }
            return;
        }

        // 若未见到验证码弹窗，则等待可能的直接下载事件
        logger.info("未见验证码弹窗，等待下载事件...");
        Download direct = tryWaitForDownload(page, () -> {}, 8_000);
        if (direct != null) {
            saveDownload(direct, targetDir, row.displayFileName);
            return;
        }

        // 仍未触发下载，最后再次检查是否弹出验证码并处理（老代码路径）
        if (isDownloadCaptchaVisible(page)) {
            logger.info("延时后检测到验证码（老代码路径），开始处理...");
            boolean passed = trySolveDownloadCaptchaOld(page);
            if (!passed) {
                logger.warn("验证码处理失败或未通过(老代码路径): {}", row.displayFileName);
                return;
            }
            Download dl2 = tryWaitForDownload(page, () -> finalPrimary.first().click(), 12_000);
            if (dl2 != null) {
                saveDownload(dl2, targetDir, row.displayFileName);
            } else {
                logger.warn("[captcha] 通过后未捕获下载事件(老代码路径)，尝试HTTP回退...");
                try { if (tryHttpDownload(page.context(), page, finalPrimary.first(), targetDir)) return; } catch (Exception ignore) {}
            }
            return;
        }

        logger.warn("既未出现验证码也未触发下载: {}", row.displayFileName);
    }

    // === 老代码下载钩子与HTTP回退 ===
    private void registerDownloadHooks(Page page, Path downloadDir,
                                       java.util.concurrent.atomic.AtomicBoolean suppressHook,
                                       java.util.concurrent.atomic.AtomicBoolean completed,
                                       java.util.Set<String> savedByUrl) {
        try {
            page.onDownload(download -> {
                try {
                    if (suppressHook != null && suppressHook.get()) return;
                    if (completed != null && completed.get()) return;
                    if (saveDownload(download, downloadDir, null)) {
                        if (completed != null) completed.set(true);
                    }
                } catch (Exception ignore) {}
            });
            page.onResponse(response -> {
                try {
                    if (suppressHook != null && suppressHook.get()) return;
                    if (completed != null && completed.get()) return;
                    if (saveAttachmentFromResponse(response, downloadDir, savedByUrl)) {
                        if (completed != null) completed.set(true);
                    }
                } catch (Exception ignore) {}
            });
        } catch (Exception e) {
            logger.debug("注册下载监听失败: {}", e.getMessage());
        }
    }

    private boolean saveAttachmentFromResponse(Response response, Path downloadDir, java.util.Set<String> savedByUrl) {
        try {
            if (!isAttachmentLikeResponse(response)) return false;

            String url = response.url();
            if (url == null || url.isBlank()) return false;

            if (savedByUrl != null && savedByUrl.contains(url)) return false;

            byte[] body = response.body();
            if (body == null || body.length == 0) return false;

            String fileName = extractFilename(response, url);
            if (fileName == null || fileName.isBlank()) {
                fileName = "unknown_file_" + System.currentTimeMillis();
            }

            Path savePath = uniquePath(downloadDir.resolve(sanitizeFileName(fileName)));
            java.nio.file.Files.createDirectories(savePath.getParent());
            java.nio.file.Files.write(savePath, body);
            long size = java.nio.file.Files.size(savePath);
            if (size > 0) {
                if (savedByUrl != null) savedByUrl.add(url);
                logger.info("[hook] 已通过响应流保存附件: {} ({} bytes)", savePath.toAbsolutePath(), size);
                recordSavedFile(downloadDir, savePath);
                return true;
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean isAttachmentLikeResponse(Response response) {
        try {
            int status = response.status();
            if (status < 200 || status >= 400) return false;
            String url = response.url();
            String urlLower = url == null ? "" : url.toLowerCase();
            String ct = headerIgnoreCase(response, "content-type");
            String cd = headerIgnoreCase(response, "content-disposition");
            boolean dispositionAttachment = cd != null && cd.toLowerCase().contains("attachment");
            boolean dispositionHasFilename = cd != null && cd.toLowerCase().contains("filename");
            boolean urlLooksFile = urlLower.endsWith(".pdf") || urlLower.endsWith(".doc") || urlLower.endsWith(".docx") ||
                    urlLower.endsWith(".xls") || urlLower.endsWith(".xlsx") || urlLower.endsWith(".zip") || urlLower.endsWith(".rar");
            boolean contentTypeFile = ct != null && (
                    ct.toLowerCase().startsWith("application/") ||
                            ct.toLowerCase().contains("pdf") ||
                            ct.toLowerCase().contains("msword") ||
                            ct.toLowerCase().contains("officedocument") ||
                            ct.toLowerCase().contains("zip") ||
                            ct.toLowerCase().contains("rar")
            );
            return dispositionAttachment || dispositionHasFilename || urlLooksFile || contentTypeFile;
        } catch (Exception e) {
            return false;
        }
    }

    private String headerIgnoreCase(Response response, String name) {
        try {
            if (response.headers() == null) return null;
            String v = response.headers().get(name);
            if (v != null) return v;
            v = response.headers().get(name.toLowerCase());
            if (v != null) return v;
            v = response.headers().get(name.toUpperCase());
            return v;
        } catch (Exception e) {
            return null;
        }
    }

    private String extractFilename(Response response, String url) {
        String cd = headerIgnoreCase(response, "content-disposition");
        if (cd != null) {
            try {
                String lower = cd.toLowerCase();
                int idxStar = lower.indexOf("filename*=");
                if (idxStar >= 0) {
                    String part = cd.substring(idxStar + 9).trim();
                    String encoded = part.substring(Math.max(part.lastIndexOf("''") + 2, idxStar + 9)).trim();
                    encoded = encoded.replaceAll("^[\"]|[\"]$", "");
                    try { return java.net.URLDecoder.decode(encoded, java.nio.charset.StandardCharsets.UTF_8); } catch (Exception ignore) {}
                }
                int idx = lower.indexOf("filename=");
                if (idx >= 0) {
                    String part = cd.substring(idx + 9).trim();
                    if (part.startsWith("\"")) {
                        int end = part.indexOf("\"", 1);
                        if (end > 1) return part.substring(1, end);
                    }
                    int sc = part.indexOf(';');
                    if (sc > 0) part = part.substring(0, sc);
                    return part;
                }
            } catch (Exception ignore) {}
        }
        try {
            String path = url;
            int q = path.indexOf('?');
            if (q >= 0) path = path.substring(0, q);
            int slash = path.lastIndexOf('/');
            String name = (slash >= 0) ? path.substring(slash + 1) : path;
            return java.net.URLDecoder.decode(name, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
    }

    private void recordSavedFile(Path downloadDir, Path savedPath) {
        try {
            Path index = downloadDir.resolve("_saved_files.txt");
            java.nio.file.Files.createDirectories(downloadDir);
            String line = java.time.LocalDateTime.now() + "\t" + savedPath.toAbsolutePath() + System.lineSeparator();
            java.nio.file.Files.write(index, line.getBytes(java.nio.charset.StandardCharsets.UTF_8), java.nio.file.StandardOpenOption.CREATE, java.nio.file.StandardOpenOption.APPEND);
        } catch (Exception ignore) { }
    }

    private boolean tryHttpDownload(BrowserContext context, Page page, Locator anchor, Path downloadDir) {
        try {
            String href = null;
            try { href = anchor.getAttribute("href"); } catch (Exception ignore) {}
            if (href == null || href.isBlank()) return false;
            String absUrl = toAbsoluteUrl(page.url(), href);
            if (absUrl == null) return false;

            okhttp3.OkHttpClient client = new okhttp3.OkHttpClient();
            okhttp3.Request.Builder rb = new okhttp3.Request.Builder().url(absUrl).get()
                    .addHeader("User-Agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome Safari")
                    .addHeader("Accept", "*/*");
            String cookieHeader = buildCookieHeader(context, absUrl);
            if (cookieHeader != null) rb.addHeader("Cookie", cookieHeader);

            try (okhttp3.Response resp = client.newCall(rb.build()).execute()) {
                if (!resp.isSuccessful() || resp.body() == null) return false;
                String fname = null;
                String cd = resp.header("Content-Disposition");
                if (cd != null && cd.toLowerCase().contains("filename")) {
                    fname = parseFilenameFromContentDisposition(cd);
                }
                if (fname == null || fname.isBlank()) {
                    fname = extractNameFromUrl(absUrl);
                }
                if (fname == null || fname.isBlank()) {
                    fname = "unknown_file_" + System.currentTimeMillis();
                }
                Path savePath = uniquePath(downloadDir.resolve(sanitizeFileName(fname)));
                byte[] bytes = resp.body().bytes();
                if (bytes == null || bytes.length == 0) return false;
                java.nio.file.Files.createDirectories(savePath.getParent());
                java.nio.file.Files.write(savePath, bytes);
                long size = java.nio.file.Files.size(savePath);
                if (size > 0) {
                    logger.info("[http] 已保存附件: {} ({} bytes)", savePath.toAbsolutePath(), size);
                    recordSavedFile(downloadDir, savePath);
                    return true;
                }
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    private String parseFilenameFromContentDisposition(String cd) {
        try {
            String lower = cd.toLowerCase();
            int idxStar = lower.indexOf("filename*=");
            if (idxStar >= 0) {
                String part = cd.substring(idxStar + 9).trim();
                String encoded = part.substring(Math.max(part.lastIndexOf("''") + 2, idxStar + 9)).trim();
                encoded = encoded.replaceAll("^[\"]|[\"]$", "");
                try { return java.net.URLDecoder.decode(encoded, java.nio.charset.StandardCharsets.UTF_8); } catch (Exception ignore) {}
            }
            int idx = lower.indexOf("filename=");
            if (idx >= 0) {
                String part = cd.substring(idx + 9).trim();
                if (part.startsWith("\"")) {
                    int end = part.indexOf("\"", 1);
                    if (end > 1) return part.substring(1, end);
                }
                int sc = part.indexOf(';');
                if (sc > 0) part = part.substring(0, sc);
                return part;
            }
        } catch (Exception ignore) {}
        return null;
    }

    private String extractNameFromUrl(String url) {
        try {
            String path = url;
            int q = path.indexOf('?');
            if (q >= 0) path = path.substring(0, q);
            int slash = path.lastIndexOf('/');
            String name = (slash >= 0) ? path.substring(slash + 1) : path;
            return java.net.URLDecoder.decode(name, java.nio.charset.StandardCharsets.UTF_8);
        } catch (Exception e) {
            return null;
        }
    }

    private String buildCookieHeader(BrowserContext context, String absUrl) {
        try {
            java.util.List<com.microsoft.playwright.options.Cookie> cookies = context.cookies(absUrl);
            if (cookies == null || cookies.isEmpty()) return null;
            StringBuilder sb = new StringBuilder();
            for (com.microsoft.playwright.options.Cookie c : cookies) {
                if (sb.length() > 0) sb.append("; ");
                sb.append(c.name).append("=").append(c.value);
            }
            return sb.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private String toAbsoluteUrl(String base, String href) {
        try {
            if (href.startsWith("http://") || href.startsWith("https://")) return href;
            java.net.URI baseUri = java.net.URI.create(base);
            java.net.URI res = baseUri.resolve(href);
            return res.toString();
        } catch (Exception e) {
            return null;
        }
    }

    private Download tryWaitForDownload(Page page, Runnable trigger, int timeoutMs) {
        try {
            Page.WaitForDownloadOptions opts = new Page.WaitForDownloadOptions();
            if (timeoutMs > 0) opts.setTimeout((double) timeoutMs);
            logger.debug("等待下载事件(timeout={}ms)...", timeoutMs);
            Download d = page.waitForDownload(opts, trigger);
            logger.debug("下载事件捕获: {}", d != null);
            return d;
        } catch (Exception e) {
            logger.debug("等待下载事件超时/失败: {}", e.getMessage());
            return null;
        }
    }

    private boolean solveCaptchaAndDownload(Page page, Path targetDir, String displayName) throws IOException {
        // 采用“点击验证码图片刷新 -> 重新截图 -> OCR -> 提交”的重试策略
        for (int attempt = 1; attempt <= 3; attempt++) {
            CaptchaContext ctx = findCaptchaContext(page);
            if (ctx == null) return false;

            // 每轮开始先点击图片刷新一次，确保拿到新验证码
            logger.info("[captcha] 第{}次尝试: 刷新验证码", attempt);
            refreshCaptcha(ctx, page);

            // 截图 -> OCR
            Path tmp = Files.createTempFile("yngp_captcha_", ".png");
            Path debugCopy = null;
            try {
                waitForImageReady(ctx.img);
                byte[] imgBytes = ctx.img.screenshot();
                Files.write(tmp, imgBytes);
                // 额外保存一份到目标目录，便于现场核对
                try {
                    debugCopy = uniquePath(targetDir.resolve("__captcha_attempt_" + attempt + ".png"));
                    Files.write(debugCopy, imgBytes);
                    logger.info("[captcha] 截图完成: {} ({} bytes)", debugCopy.toAbsolutePath(), imgBytes.length);
                } catch (Exception ignore) {}
            } catch (Exception e) {
                logger.warn("[captcha] 验证码截图失败: {}", e.getMessage());
                refreshCaptcha(ctx, page);
                continue;
            }

            // 选择用于 OCR 的图片路径：优先使用 debugDump 产生的 captcha_img.png 其后是本次尝试文件，最后是临时文件
            Path preferred = chooseCaptchaImagePath(targetDir, debugCopy, tmp);
            String endpointUsed = (ocrEndpoint == null || ocrEndpoint.isBlank()) ? "http://127.0.0.1:18080" : ocrEndpoint;
            logger.info("[captcha] 调用OCR服务: {}，图片= {}", endpointUsed, preferred.toAbsolutePath());
            String ocr = (ocrEndpoint == null || ocrEndpoint.isBlank()) ? OcrClient.recognize(preferred) : OcrClient.recognize(ocrEndpoint, preferred);
            if (ocr == null || ocr.isBlank()) {
                logger.info("[captcha] OCR 返回空，重试({})", attempt);
                refreshCaptcha(ctx, page);
                continue;
            }
            String code = ocr.replaceAll("[^a-zA-Z0-9]", "");
            logger.info("[captcha] OCR 原始='{}' 规范化='{}'", ocr, code);
            if (code.isBlank()) {
                refreshCaptcha(ctx, page);
                continue;
            }

            try {
                // 确保输入框聚焦
                try { ctx.input.focus(); } catch (Exception ignore) {}
                ctx.input.fill("");
                ctx.input.fill(code);
                logger.info("[captcha] 已填写验证码");
            } catch (Exception e) { logger.warn("[captcha] 填写验证码失败: {}", e.getMessage()); refreshCaptcha(ctx, page); continue; }

            // 提交并等待弹窗关闭（成功准入的判断），不直接等待下载事件
            try {
                logger.info("[captcha] 提交验证码，等待弹窗关闭...");
                ctx.ok.click();
                if (waitCaptchaModalClosed(page, 3000)) {
                    logger.info("[captcha] 弹窗已关闭，判定验证码通过");
                    return true; // 由外层再点击下载
                } else {
                    logger.info("[captcha] 弹窗仍在，可能验证码错误，读取错误提示...");
                    try {
                        if (ctx.panel != null) {
                            String err = null;
                            try { err = ctx.panel.locator(".errormg").innerText(); } catch (Exception ignore) {}
                            if (err != null && !err.isBlank()) { logger.info("[captcha] 错误提示: {}", err.trim()); }
                        }
                    } catch (Exception ignore) {}
                    refreshCaptcha(ctx, page);
                }
            } catch (Exception e) {
                logger.info("[captcha] 提交异常: {}，重试({})", e.getMessage(), attempt);
                refreshCaptcha(ctx, page);
            }
        }
        return false;
    }

    private void refreshCaptcha(CaptchaContext ctx, Page page) {
        try {
            ctx.img.click();
            page.waitForTimeout(800);
            logger.debug("[captcha] 已点击图片刷新");
        } catch (Exception e) {
            logger.debug("[captcha] 点击图片刷新失败: {}", e.getMessage());
            try {
                if (ctx.refresh != null) { ctx.refresh.click(); page.waitForTimeout(800); logger.debug("[captcha] 已点击刷新链接"); }
            } catch (Exception ignore) {}
        }
    }

    private void waitForImageReady(Locator img) {
        try {
            // 等待图片 src 存在且元素尺寸>0，避免空白截图
            // 等待元素可见
            img.waitFor(new Locator.WaitForOptions().setTimeout(2000));
            // 轮询 src 与大小
            for (int i = 0; i < 5; i++) {
                String src = null;
                try { src = img.getAttribute("src"); } catch (Exception ignore) {}
                boolean sizeOk = false;
                try {
                    Object box = img.evaluate("e => ({w: e.naturalWidth || e.clientWidth, h: e.naturalHeight || e.clientHeight})");
                    if (box != null) {
                        String s = String.valueOf(box);
                        sizeOk = s.contains("w=") || s.contains("h=");
                    }
                } catch (Exception ignore) {}
                if (src != null && !src.isBlank() && sizeOk) return;
                try { Thread.sleep(200); } catch (InterruptedException ignore) {}
            }
        } catch (Exception ignore) {}
    }

    private CaptchaContext findCaptchaContext(Page page) {
        // 优先在可见的 bootstrap-dialog 中查找，确保聚焦模态框
        try {
            Locator modal = page.locator(".bootstrap-dialog");
            if (modal.count() > 0) {
                Locator visibleModal = modal.first();
                try {
                    if (modal.count() > 1) {
                        // 选第一个可见的
                        for (int i = 0; i < modal.count(); i++) {
                            Locator m = modal.nth(i);
                            if (m.isVisible()) { visibleModal = m; break; }
                        }
                    }
                } catch (Exception ignore) {}

                Locator img = visibleModal.locator("#imgObj");
                Locator input = visibleModal.locator("#veryCode");
                Locator ok = visibleModal.locator("#doSubmit, .btn-primary");
                Locator refresh = visibleModal.locator("a[onclick*=changeImg], .verify-refresh, text=换一张, text=刷新");
                logger.info("[captcha] 模态框内元素: img={}, input={}, ok={}", img.count(), input.count(), ok.count());
                if (img.count() > 0 && input.count() > 0 && ok.count() > 0) {
                    return new CaptchaContext(img.first(), input.first(), ok.first(), refresh.count() > 0 ? refresh.first() : null, visibleModal);
                }
            }
        } catch (Exception ignore) {}

        // 兜底：按参考片段在主文档中查找（少数情况下对话框结构不同）
        final String panelSel = ".layui-layer .layui-layer-content, .verifybox, #mpanel4, .modal:visible";
        final String imgSel = "#imgObj, #mpanel4 .verify-img-panel img, .verify-img-panel img, img#kaptchaImage, img#captchaimg, img.kaptcha-img, img.captcha, img[alt*=验证码], img[src*='captcha'], img[src*='kaptcha']";
        final String inputSel = "#veryCode, #mpanel4 input[type='text'], .verifybox input[type='text'], input[placeholder*='验证码'], input[name*='captcha'], input[name*='kaptcha']";
        final String okSel = "#doSubmit, .verify-btn, button:has-text('确认'), button:has-text('确定'), button:has-text('提交'), .layui-layer-btn .layui-layer-btn0";
        final String refreshSel = "#mpanel4 .verify-refresh, .verify-refresh, text=换一张, text=刷新";
        try {
            Locator panel = page.locator(panelSel);
            Locator img = page.locator(imgSel);
            Locator input = page.locator(inputSel);
            Locator ok = page.locator(okSel);
            Locator refresh = page.locator(refreshSel);
            if (img.count() > 0 && input.count() > 0 && ok.count() > 0) {
                return new CaptchaContext(img.first(), input.first(), ok.first(), refresh.count() > 0 ? refresh.first() : null, panel.count() > 0 ? panel.first() : null);
            }
        } catch (Exception ignore) {}
        return null;
    }

    private boolean isCaptchaModalVisible(Page page, int timeoutMs) {
        try {
            Page.WaitForSelectorOptions opt = new Page.WaitForSelectorOptions().setTimeout((double) Math.max(0, timeoutMs));
            page.waitForSelector(".bootstrap-dialog #imgObj", opt);
            return page.isVisible(".bootstrap-dialog #imgObj");
        } catch (Exception e) {
            return false;
        }
    }

    // === 以下为“老代码”路径：保持与 old 版本一致的选择器与判定/提交方式 ===
    private boolean isDownloadCaptchaVisible(Page page) {
        try {
            String[] sels = new String[]{
                    ".layui-layer:visible", ".bootstrap-dialog:visible", ".verifybox:visible", "#mpanel4 .mask"
            };
            for (String s : sels) {
                try { if (page.isVisible(s)) return true; } catch (Exception ignore) {}
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    private boolean trySolveDownloadCaptchaOld(Page page) {
        try {
            Locator panel = page.locator(".layui-layer .layui-layer-content, .bootstrap-dialog .modal-body, .verifybox, #mpanel4, .modal:visible");
            if (panel.count() == 0) return false;

            Locator img = page.locator("#imgObj, #mpanel4 .verify-img-panel img, .verify-img-panel img, img#kaptchaImage, img#captchaimg, img.kaptcha-img, img.captcha, img[alt*=验证码], img[src*='captcha'], img[src*='kaptcha']");
            Locator input = page.locator("#veryCode, #mpanel4 input[type='text'], .verifybox input[type='text'], input[placeholder*='验证码'], input[name*='captcha'], input[name*='kaptcha']");
            Locator okBtn = page.locator("#doSubmit, .verify-btn, button:has-text('确认'), button:has-text('确定'), button:has-text('提交'), .layui-layer-btn .layui-layer-btn0, .bootstrap-dialog .btn-primary");
            Locator refreshBtn = page.locator("#mpanel4 .verify-refresh, .verify-refresh, text=换一张, text=刷新");

            if (img.count() == 0 || input.count() == 0) {
                for (Frame f : page.frames()) {
                    try {
                        Locator fImg = f.locator("#imgObj, #mpanel4 .verify-img-panel img, .verify-img-panel img, img#kaptchaImage, img#captchaimg, img.kaptcha-img, img.captcha, img[alt*=验证码], img[src*='captcha'], img[src*='kaptcha']");
                        Locator fInput = f.locator("#veryCode, #mpanel4 input[type='text'], .verifybox input[type='text'], input[placeholder*='验证码'], input[name*='captcha'], input[name*='kaptcha']");
                        if (fImg.count() > 0 && fInput.count() > 0) {
                            img = fImg; input = fInput;
                            okBtn = f.locator("#doSubmit, .verify-btn, button:has-text('确认'), button:has-text('确定'), button:has-text('提交'), .layui-layer-btn .layui-layer-btn0, .bootstrap-dialog .btn-primary");
                            refreshBtn = f.locator("#mpanel4 .verify-refresh, .verify-refresh, text=换一张, text=刷新");
                            break;
                        }
                    } catch (Exception ignore) {}
                }
            }

            if (input.count() == 0) return false;

            int attempts = 1; // 老代码默认一次
            for (int round = 0; round < attempts; round++) {
                String endpoint = (ocrEndpoint == null || ocrEndpoint.isBlank()) ? null : ocrEndpoint;
                String ocrText = ocrFromCaptchaElementOld(page, img, panel, endpoint);
                if (ocrText == null || ocrText.isBlank()) continue;
                String code = ocrText.replaceAll("[^a-zA-Z0-9]", "");
                if (code.isBlank()) continue;

                logger.info("下载验证码 OCR 识别(老代码): '{}'", code);
                input.first().fill(code);
                if (okBtn.count() > 0) okBtn.first().click(); else input.first().press("Enter");
                page.waitForTimeout(600);
                boolean stillVisible = false;
                try { if (page.isVisible(".layui-layer:visible")) stillVisible = true; } catch (Exception ignore) {}
                try { if (page.isVisible(".bootstrap-dialog:visible")) stillVisible = true; } catch (Exception ignore) {}
                try { if (page.isVisible(".verifybox:visible")) stillVisible = true; } catch (Exception ignore) {}
                try { if (page.isVisible("#mpanel4 .mask")) stillVisible = true; } catch (Exception ignore) {}
                if (!stillVisible) return true;
            }
            return false;
        } catch (Exception e) {
            logger.debug("下载验证码自动识别失败(老代码): {}", e.getMessage());
            return false;
        }
    }

    private String ocrFromCaptchaElementOld(Page page, Locator img, Locator panel, String endpoint) {
        try {
            if (img != null && img.count() > 0) {
                String src = null;
                try { src = img.first().getAttribute("src"); } catch (Exception ignore) {}
                if (src != null && !src.isBlank()) {
                    Path tmp = Files.createTempFile("yngp_dl_captcha", ".png");
                    try {
                        img.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                        return (endpoint == null) ? OcrClient.recognize(tmp) : OcrClient.recognize(endpoint, tmp);
                    } finally {
                        try { Files.deleteIfExists(tmp); } catch (Exception ignore) {}
                    }
                }
            }
            Path tmp = Files.createTempFile("yngp_dl_captcha", ".png");
            try {
                if (img != null && img.count() > 0) {
                    img.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                } else if (panel != null && panel.count() > 0) {
                    panel.first().screenshot(new Locator.ScreenshotOptions().setPath(tmp));
                } else {
                    page.screenshot(new Page.ScreenshotOptions().setPath(tmp));
                }
                return (endpoint == null) ? OcrClient.recognize(tmp) : OcrClient.recognize(endpoint, tmp);
            } finally {
                try { Files.deleteIfExists(tmp); } catch (Exception ignore) {}
            }
        } catch (Exception e) {
            return null;
        }
    }
    private void debugDumpCaptcha(Page page, Path targetDir) {
        try {
            Files.createDirectories(targetDir);
            Locator modal = page.locator(".bootstrap-dialog");
            if (modal.count() > 0) {
                Locator visible = modal.first();
                try {
                    if (modal.count() > 1) {
                        for (int i = 0; i < modal.count(); i++) {
                            Locator m = modal.nth(i);
                            if (m.isVisible()) { visible = m; break; }
                        }
                    }
                } catch (Exception ignore) {}

                // 保存弹窗HTML
                try {
                    String html = visible.innerHTML();
                    Path dump = uniquePath(targetDir.resolve("captcha_modal_dump.html"));
                    Files.writeString(dump, html, java.nio.charset.StandardCharsets.UTF_8);
                    logger.info("[captcha] 已保存弹窗HTML: {}", dump.toAbsolutePath());
                } catch (Exception e) {
                    logger.debug("[captcha] 保存弹窗HTML失败: {}", e.getMessage());
                }

                // 保存验证码图片截图
                try {
                    Locator img = visible.locator("#imgObj");
                    waitForImageReady(img);
                    byte[] b = img.screenshot();
                    Path imgOut = targetDir.resolve("captcha_img.png");
                    try { Files.deleteIfExists(imgOut); } catch (Exception ignore) {}
                    Files.write(imgOut, b);
                    String src = null;
                    try { src = img.getAttribute("src"); } catch (Exception ignore) {}
                    logger.info("[captcha] 已保存验证码图片: {} (src={})", imgOut.toAbsolutePath(), src);
                } catch (Exception e) {
                    logger.warn("[captcha] 保存验证码图片失败: {}", e.getMessage());
                    try {
                        Path fallback = uniquePath(targetDir.resolve("captcha_modal_full.png"));
                        visible.screenshot(new Locator.ScreenshotOptions().setPath(fallback));
                        logger.info("[captcha] 已保存弹窗整体截图: {}", fallback.toAbsolutePath());
                    } catch (Exception ignore) {}
                }
            }
        } catch (Exception e) {
            logger.debug("[captcha] debugDump 失败: {}", e.getMessage());
        }
    }

    private boolean waitCaptchaModalClosed(Page page, int timeoutMs) {
        long end = System.currentTimeMillis() + Math.max(0, timeoutMs);
        while (System.currentTimeMillis() < end) {
            try {
                boolean vis = page.isVisible(".bootstrap-dialog #imgObj");
                if (!vis) return true;
            } catch (Exception ignore) {
                return true;
            }
            try { page.waitForTimeout(120); } catch (Exception ignore) {}
        }
        return false;
    }

    private Path chooseCaptchaImagePath(Path targetDir, Path attemptCopy, Path tmp) {
        try {
            Path dumpImg = targetDir.resolve("captcha_img.png");
            if (Files.exists(dumpImg)) {
                try { if (Files.size(dumpImg) > 0) return dumpImg; } catch (Exception ignore) {}
            }
        } catch (Exception ignore) {}
        if (attemptCopy != null) return attemptCopy;
        return tmp;
    }

    private boolean saveDownload(Download dl, Path targetDir, String preferredName) {
        try {
            String suggested = null;
            try { suggested = dl.suggestedFilename(); } catch (Exception ignore) {}
            String finalName = normalizePreferredName(preferredName, suggested);
            Path target = uniquePath(targetDir.resolve(finalName));
            logger.info("保存附件: {}", target.getFileName());
            dl.saveAs(target);
            return true;
        } catch (Exception e) {
            logger.warn("保存下载文件失败: {}", e.getMessage());
            return false;
        }
    }

    private static String normalizePreferredName(String display, String suggested) {
        String name = (display != null && !display.isBlank()) ? display.trim() : suggested;
        if (name == null || name.isBlank()) name = "download";
        // 若无扩展名而 suggested 提供了扩展名，则补齐
        if (!name.contains(".") && suggested != null && suggested.contains(".")) {
            String ext = suggested.substring(suggested.lastIndexOf('.'));
            name = name + ext;
        }
        return sanitizeFileName(name);
    }

    private static Path uniquePath(Path path) {
        if (!Files.exists(path)) return path;
        String fileName = path.getFileName().toString();
        String base = fileName;
        String ext = "";
        int dot = fileName.lastIndexOf('.');
        if (dot > 0) { base = fileName.substring(0, dot); ext = fileName.substring(dot); }
        for (int i = 1; i < 10_000; i++) {
            Path p = path.getParent().resolve(base + " (" + i + ")" + ext);
            if (!Files.exists(p)) return p;
        }
        return path.getParent().resolve(base + " (dup)" + ext);
    }

    private static class AttachmentRow {
        final int index;
        final String displayFileName;

        AttachmentRow(int index, String displayFileName) {
            this.index = index;
            this.displayFileName = displayFileName;
        }
    }

    private static class CaptchaContext {
        final Locator img;
        final Locator input;
        final Locator ok;
        final Locator refresh; // 可为空
        final Locator panel;   // 可为空

        CaptchaContext(Locator img, Locator input, Locator ok, Locator refresh, Locator panel) {
            this.img = img;
            this.input = input;
            this.ok = ok;
            this.refresh = refresh;
            this.panel = panel;
        }
    }

    public static void main(String[] args) {
        String links = "/Users/<USER>/Desktop/content-crawler/data/yngp/2025-08-10/files/collected_links.txt";
        boolean headless = false; // 调试建议 false
        new YngpCrawler(links, headless).start();
    }
}


