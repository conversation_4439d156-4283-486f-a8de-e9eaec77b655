package org.hujun.crawler.runner;

import org.hujun.crawler.config.AppConfig;
import org.hujun.crawler.websites.cambridge.crawler.CambridgeUnifiedCrawler;
import org.hujun.crawler.websites.yngp.crawler.YngpCrawler;
import org.hujun.crawler.websites.yngp.crawler.YngpLinkCollector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 爬虫运行器
 * 负责管理各种网站的爬虫任务
 */
@Component
public class CrawlerRunner implements Runner {
    
    private static final Logger logger = LoggerFactory.getLogger(CrawlerRunner.class);
    
    @Autowired
    private AppConfig appConfig;
    
    @Override
    public String getName() {
        return "crawler";
    }
    
    @Override
    public String getDescription() {
        return "网站内容爬虫 (支持Cambridge、MyELT等)";
    }
    
    @Override
    public boolean shouldRun(String[] args) {
        logger.debug("CrawlerRunner: 检查参数是否匹配");
        logger.debug("CrawlerRunner: 参数数组长度: {}", args.length);
        
        // 详细打印每个参数
        for (int i = 0; i < args.length; i++) {
            logger.debug("CrawlerRunner: args[{}] = '{}'", i, args[i]);
        }
        
        // 处理Maven传递的逗号分隔参数
        String[] actualArgs = normalizeArgs(args);
        logger.debug("CrawlerRunner: 归一化后参数长度: {}", actualArgs.length);
        for (int i = 0; i < actualArgs.length; i++) {
            logger.debug("CrawlerRunner: actualArgs[{}] = '{}'", i, actualArgs[i]);
        }
        
        // 检查第一个参数是否为 crawler
        if (actualArgs.length > 0) {
            String firstArg = actualArgs[0];
            logger.debug("CrawlerRunner: 第一个参数是: '{}', 与'crawler'比较结果: {}", 
                firstArg, firstArg.equals("crawler"));
            
            if (firstArg.equals("crawler")) {
                logger.debug("CrawlerRunner: 第一个参数匹配 'crawler', 返回 true");
                return true;
            }
        }
        
        // 兼容 profile 参数在前：允许 'crawler' 出现在任意位置
        for (String arg : actualArgs) {
            if ("crawler".equals(arg)) {
                logger.debug("CrawlerRunner: 在参数中找到 'crawler'，返回 true");
                return true;
            }
        }
        
        // 如果配置文件中启用了爬虫，也会运行
        if (appConfig.getCrawler().isEnabled()) {
            logger.debug("CrawlerRunner: 配置文件中启用了爬虫，返回 true");
            return true;
        }
        
        logger.debug("CrawlerRunner: 没有匹配的参数，返回 false");
        return false;
    }
    
    @Override
    public void run(String[] args) throws Exception {
        CrawlerConfig config = parseArguments(args);
        
        logger.info("开始爬虫任务 - 网站: {}, 用户: {}, 无头模式: {}", 
            config.website, config.username, config.headless);
        
        switch (config.website.toLowerCase()) {
            case "cambridge":
                runCambridgeCrawler(config);
                break;
            case "myelt":
                // TODO: 实现MyELT爬虫
                logger.warn("MyELT爬虫尚未实现");
                break;
            case "yngp":
                runYngpCrawler(config, args);
                break;
            case "yngp-captcha":
                runYngpCaptcha(args, config);
                break;
            case "yngp-dl-captcha":
                runYngpDownloadCaptcha(args, config);
                break;
            case "yngp-links":
                runYngpLinkCollector(args, config);
                break;
            default:
                logger.error("不支持的网站: {}", config.website);
                printHelp();
        }
    }
    
    @Override
    public void printHelp() {
        System.out.println("\n=== 爬虫模块使用说明 ===");
        System.out.println("用法: java -jar content-crawler.jar crawler [选项]");
        System.out.println();
        System.out.println("选项:");
        System.out.println("  --website=WEBSITE            指定爬虫网站 (cambridge, myelt, yngp)");
        System.out.println("  --username=USERNAME          登录用户名");
        System.out.println("  --password=PASSWORD          登录密码");
        System.out.println("  --headless=true|false        是否使用无头模式 (默认: true)");
        System.out.println("  --sections=SECTIONS          指定爬取的模块 (READING,WRITING,LISTENING 或 ALL)");
        System.out.println("  --ocr=URL                    OCR 服务地址（用于验证码识别，可选，例如 https://dedb612a1e16.ngrok-free.app）");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  # 爬取Cambridge所有内容");
        System.out.println("  java -jar content-crawler.jar crawler --website=cambridge");
        System.out.println();
        System.out.println("  # 只爬取Writing模块");
        System.out.println("  java -jar content-crawler.jar crawler --website=cambridge --sections=WRITING");
        System.out.println();
        System.out.println("  # 使用自定义账户信息");
        System.out.println("  java -jar content-crawler.jar crawler --website=cambridge --username=user --password=pass");
        System.out.println();
        System.out.println("  # 爬取云南省政府采购网（需要人工通过验证码），自定义输出目录");
        System.out.println("  java -jar content-crawler.jar crawler --website=yngp --headless=false --out=/abs/path/to/output");
        System.out.println();
        System.out.println("  # 采集云南省政府采购网验证码样本（默认200张）");
        System.out.println("  java -jar content-crawler.jar crawler --website=yngp-captcha --headless=false --count=500 --out=/path/to/save --interval=800");
        System.out.println();
        System.out.println("  # 采集‘下载文件前’的验证码（建议提供详情页URL以绕过列表页验证码）");
        System.out.println("  java -jar content-crawler.jar crawler --website=yngp-dl-captcha --headless=false --detail=DETAIL_URL --count=300 --out=/path/to/save --interval=800");
        System.out.println();
        System.out.println("  # 仅采集列表标题链接（每页前10条，翻到第2000页，遇验证码人工处理）");
        System.out.println("  java -jar content-crawler.jar crawler --website=yngp-links --headless=false --out=/abs/path/collected_links.txt --pages=2000 --limit=10");
        System.out.println();
    }
    
    private CrawlerConfig parseArguments(String[] args) {
        CrawlerConfig config = new CrawlerConfig();
        
        // 从配置文件获取默认值
        config.username = appConfig.getCrawler().getUsername();
        config.password = appConfig.getCrawler().getPassword();
        config.headless = appConfig.getCrawler().isHeadless();
        config.website = "cambridge"; // 默认网站
        config.sections = "ALL"; // 默认爬取所有模块
        
        // 统一归一化参数
        String[] actualArgs = normalizeArgs(args);
        
        // 解析命令行参数
        for (String arg : actualArgs) {
            if (arg.startsWith("--website=")) {
                config.website = arg.substring("--website=".length());
            } else if (arg.startsWith("--username=")) {
                config.username = arg.substring("--username=".length());
            } else if (arg.startsWith("--password=")) {
                config.password = arg.substring("--password=".length());
            } else if (arg.startsWith("--headless=")) {
                config.headless = Boolean.parseBoolean(arg.substring("--headless=".length()));
            } else if (arg.startsWith("--sections=")) {
                config.sections = arg.substring("--sections=".length()).toUpperCase();
            }
        }
        
        return config;
    }
    
    private void runCambridgeCrawler(CrawlerConfig config) throws Exception {
        // 设置Playwright系统属性，避免Chrome崩溃
        System.setProperty("playwright.browser.chromium.args",
                "--disable-gpu,--no-sandbox,--disable-dev-shm-usage,--disable-extensions");
        
        logger.info("正在初始化Cambridge爬虫，这可能需要一些时间...");
        CambridgeUnifiedCrawler crawler = new CambridgeUnifiedCrawler();
        
        // 根据sections参数选择运行方法
        switch (config.sections) {
            case "WRITING":
                crawler.runWriting(config.username, config.password, config.headless);
                break;
            case "LISTENING":
                crawler.runListening(config.username, config.password, config.headless);
                break;
            case "READING":
                crawler.runReading(config.username, config.password, config.headless);
                break;
            case "ALL":
            default:
                crawler.runAll(config.username, config.password, config.headless);
                break;
        }
        
        logger.info("Cambridge爬虫任务完成");
    }

    private void runYngpCrawler(CrawlerConfig config, String[] args) {
        logger.info("正在初始化YNGP爬虫...");

        // 解析必要参数
        String[] actualArgs = normalizeArgs(args);
        String links = null;
        String ocr = null;
        for (String arg : actualArgs) {
            if (arg.startsWith("--links=")) {
                links = arg.substring("--links=".length());
            } else if (arg.startsWith("--ocr=")) {
                ocr = arg.substring("--ocr=".length());
            }
        }

        // 默认链接文件（若未提供）
        if (links == null || links.isBlank()) {
            links = "/Users/<USER>/Desktop/content-crawler/data/yngp/2025-08-10/files/collected_links.txt";
        }

        new YngpCrawler(links, config.headless, ocr).start();
        logger.info("YNGP爬虫任务完成");
    }

    private void runYngpCaptcha(String[] args, CrawlerConfig config) {
        logger.info("正在初始化YNGP验证码采集器...");

        int count = 200;
        long interval = 700;
        String out = null;

        String[] actualArgs = normalizeArgs(args);
        for (String arg : actualArgs) {
            if (arg.startsWith("--count=")) {
                try { count = Integer.parseInt(arg.substring("--count=".length())); } catch (NumberFormatException ignored) {}
            } else if (arg.startsWith("--interval=")) {
                try { interval = Long.parseLong(arg.substring("--interval=".length())); } catch (NumberFormatException ignored) {}
            } else if (arg.startsWith("--out=")) {
                out = arg.substring("--out=".length());
            }
        }
    }

    private void runYngpDownloadCaptcha(String[] args, CrawlerConfig config) {
        logger.info("正在初始化YNGP下载验证码采集器...");

        int count = 200;
        long interval = 800;
        String out = null;
        String detail = null;

        String[] actualArgs = normalizeArgs(args);
        for (String arg : actualArgs) {
            if (arg.startsWith("--count=")) {
                try { count = Integer.parseInt(arg.substring("--count=".length())); } catch (NumberFormatException ignored) {}
            } else if (arg.startsWith("--interval=")) {
                try { interval = Long.parseLong(arg.substring("--interval=".length())); } catch (NumberFormatException ignored) {}
            } else if (arg.startsWith("--out=")) {
                out = arg.substring("--out=".length());
            } else if (arg.startsWith("--detail=")) {
                detail = arg.substring("--detail=".length());
            }
        }

    }

    private void runYngpLinkCollector(String[] args, CrawlerConfig config) {
        logger.info("正在初始化YNGP链接采集器...");

        String out = null;
        Integer pages = null;
        Integer limit = null;

        String[] actualArgs = args;
        if (args.length == 1 && args[0].contains(",")) {
            actualArgs = args[0].split(",");
        }
        for (String arg : actualArgs) {
            if (arg.startsWith("--out=")) {
                out = arg.substring("--out=".length());
            } else if (arg.startsWith("--pages=")) {
                try { pages = Integer.parseInt(arg.substring("--pages=".length())); } catch (NumberFormatException ignored) {}
            } else if (arg.startsWith("--limit=")) {
                try { limit = Integer.parseInt(arg.substring("--limit=".length())); } catch (NumberFormatException ignored) {}
            }
        }

        YngpLinkCollector.Options opt = new YngpLinkCollector.Options();
        opt.headless = config.headless;
        if (out != null) opt.outputFile = out;
        if (pages != null) opt.maxPages = pages;
        if (limit != null) opt.perPageLimit = limit;

        new YngpLinkCollector().run(opt);
        logger.info("YNGP链接采集任务完成");
    }
    
    /**
     * 爬虫配置类
     */
    private static class CrawlerConfig {
        String website;
        String username;
        String password;
        boolean headless;
        String sections;
    }

    /**
     * 归一化参数：
     * - 拆分逗号分隔的单个参数（mvn spring-boot:run 常见）
     * - 过滤 Spring 注入的 profile 参数，避免干扰 Runner 识别
     */
    private String[] normalizeArgs(String[] args) {
        java.util.List<String> flattened = new java.util.ArrayList<>();
        if (args != null) {
            for (String arg : args) {
                if (arg == null) continue;
                // 过滤 Spring Boot 插件注入的 profile 参数
                if (arg.startsWith("--spring.profiles.active=")) {
                    continue;
                }
                if (arg.contains(",")) {
                    String[] parts = arg.split(",");
                    for (String p : parts) {
                        if (p != null && !p.isBlank()) {
                            flattened.add(p.trim());
                        }
                    }
                } else {
                    flattened.add(arg);
                }
            }
        }
        return flattened.toArray(new String[0]);
    }
} 