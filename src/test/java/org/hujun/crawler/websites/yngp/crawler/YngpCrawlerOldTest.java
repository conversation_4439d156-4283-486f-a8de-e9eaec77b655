package org.hujun.crawler.websites.yngp.crawler;

import java.lang.reflect.Method;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * 测试改造后的YngpCrawler_old类
 */
public class YngpCrawlerOldTest {

    @Test
    public void testExtractProjectNumber() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();

        // 使用反射访问私有方法
        Method extractMethod = YngpCrawler_old.class.getDeclaredMethod("extractProjectNumber",
                com.microsoft.playwright.Page.class);
        extractMethod.setAccessible(true);

        // 创建模拟的HTML内容
        String testHtml = "<html><body>" +
                "<p><font size=\"4px\">项目编号：KMZC2025-G1-01997-YNZZ-0098</font></p>" +
                "<p>其他内容</p>" +
                "</body></html>";

        // 注意：这个测试需要实际的Page对象，这里只是展示测试结构
        // 在实际测试中，需要使用Playwright创建Page对象并设置HTML内容
        System.out.println("项目编号提取功能测试结构已创建");
        System.out.println("预期提取结果：KMZC2025-G1-01997-YNZZ-0098");
    }

    @Test
    public void testProjectNumberRegex() {
        // 测试正则表达式匹配
        String html = "<p><font size=\"4px\">项目编号：KMZC2025-G1-01997-YNZZ-0098</font></p>";

        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "项目编号[：:][\\s]*([A-Z0-9\\-]+)",
                java.util.regex.Pattern.CASE_INSENSITIVE);
        java.util.regex.Matcher matcher = pattern.matcher(html);

        Assertions.assertTrue(matcher.find(), "应该能找到项目编号");
        String projectNumber = matcher.group(1).trim();
        Assertions.assertEquals("KMZC2025-G1-01997-YNZZ-0098", projectNumber, "项目编号应该匹配");

        System.out.println("提取到的项目编号: " + projectNumber);
    }

    @Test
    public void testSanitizeMethod() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();

        // 使用反射访问私有方法
        Method sanitizeMethod = YngpCrawler_old.class.getDeclaredMethod("sanitize", String.class);
        sanitizeMethod.setAccessible(true);

        // 测试文件名清理
        String result1 = (String) sanitizeMethod.invoke(crawler, "KMZC2025-G1-01997-YNZZ-0098");
        Assertions.assertEquals("KMZC2025-G1-01997-YNZZ-0098", result1, "正常项目编号不应该被修改");

        String result2 = (String) sanitizeMethod.invoke(crawler, "项目/编号:测试*文件?名");
        Assertions.assertFalse(result2.contains("/"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains(":"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains("*"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains("?"), "应该移除非法字符");

        System.out.println("清理后的文件名: " + result2);
    }

    @Test
    public void testExtractFileNameFromJavaScriptDownload() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();

        // 使用反射访问私有方法
        Method extractMethod = YngpCrawler_old.class.getDeclaredMethod("extractFileNameFromJavaScriptDownload",
                String.class);
        extractMethod.setAccessible(true);

        // 测试正常的javascript:download函数
        String jsCode1 = "javascript:download('1ef04861.198895699f8.-7455','/2025-08-10/070001/1754809621604.doc','（定稿）昆明市第一人民医院2025年上半年自筹经费设备购置项目（第二批）.doc')";
        String result1 = (String) extractMethod.invoke(crawler, jsCode1);
        Assertions.assertEquals("（定稿）昆明市第一人民医院2025年上半年自筹经费设备购置项目（第二批）.doc", result1, "应该正确提取文件名");

        // 测试带双引号的情况
        String jsCode2 = "javascript:download(\"id\",\"/path\",\"测试文件.pdf\")";
        String result2 = (String) extractMethod.invoke(crawler, jsCode2);
        Assertions.assertEquals("测试文件.pdf", result2, "应该正确提取带双引号的文件名");

        // 测试无效的情况
        String jsCode3 = "javascript:download('id','/path')";
        String result3 = (String) extractMethod.invoke(crawler, jsCode3);
        Assertions.assertNull(result3, "参数不足时应该返回null");

        System.out.println("JavaScript文件名提取测试通过");
        System.out.println("提取结果1: " + result1);
        System.out.println("提取结果2: " + result2);
    }

    @Test
    public void testIsGarbledText() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();

        // 使用反射访问私有方法
        Method isGarbledMethod = YngpCrawler_old.class.getDeclaredMethod("isGarbledText", String.class);
        isGarbledMethod.setAccessible(true);

        // 测试正常的中文文件名
        String normalText = "2025年丘北县八道哨乡农村人居环境改造提升项目（招标文件）.doc";
        Boolean result1 = (Boolean) isGarbledMethod.invoke(crawler, normalText);
        Assertions.assertFalse(result1, "正常中文文件名不应该被识别为乱码");

        // 测试乱码文件名
        String garbledText = "2025骞翠笜鍖楀幙鍏亾鍝ㄤ埂鍐滄潙浜哄眳鐜鏀归�鎻愬崌椤圭洰锛堟嫑鏍囨枃浠讹級.doc";
        Boolean result2 = (Boolean) isGarbledMethod.invoke(crawler, garbledText);
        Assertions.assertTrue(result2, "乱码文件名应该被正确识别");

        // 测试英文文件名
        String englishText = "project_document.pdf";
        Boolean result3 = (Boolean) isGarbledMethod.invoke(crawler, englishText);
        Assertions.assertFalse(result3, "英文文件名不应该被识别为乱码");

        // 测试空文件名
        Boolean result4 = (Boolean) isGarbledMethod.invoke(crawler, "");
        Assertions.assertFalse(result4, "空文件名不应该被识别为乱码");

        System.out.println("乱码检测测试通过");
        System.out.println("正常文件名: " + normalText + " -> " + result1);
        System.out.println("乱码文件名: " + garbledText + " -> " + result2);
        System.out.println("英文文件名: " + englishText + " -> " + result3);
    }
}
