package org.hujun.crawler.websites.yngp.crawler;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
import java.lang.reflect.Method;

/**
 * 测试改造后的YngpCrawler_old类
 */
public class YngpCrawlerOldTest {

    @Test
    public void testExtractProjectNumber() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();
        
        // 使用反射访问私有方法
        Method extractMethod = YngpCrawler_old.class.getDeclaredMethod("extractProjectNumber", 
            com.microsoft.playwright.Page.class);
        extractMethod.setAccessible(true);
        
        // 创建模拟的HTML内容
        String testHtml = "<html><body>" +
            "<p><font size=\"4px\">项目编号：KMZC2025-G1-01997-YNZZ-0098</font></p>" +
            "<p>其他内容</p>" +
            "</body></html>";
        
        // 注意：这个测试需要实际的Page对象，这里只是展示测试结构
        // 在实际测试中，需要使用Playwright创建Page对象并设置HTML内容
        System.out.println("项目编号提取功能测试结构已创建");
        System.out.println("预期提取结果：KMZC2025-G1-01997-YNZZ-0098");
    }
    
    @Test
    public void testProjectNumberRegex() {
        // 测试正则表达式匹配
        String html = "<p><font size=\"4px\">项目编号：KMZC2025-G1-01997-YNZZ-0098</font></p>";
        
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
            "项目编号[：:][\\s]*([A-Z0-9\\-]+)", 
            java.util.regex.Pattern.CASE_INSENSITIVE
        );
        java.util.regex.Matcher matcher = pattern.matcher(html);
        
        Assertions.assertTrue(matcher.find(), "应该能找到项目编号");
        String projectNumber = matcher.group(1).trim();
        Assertions.assertEquals("KMZC2025-G1-01997-YNZZ-0098", projectNumber, "项目编号应该匹配");
        
        System.out.println("提取到的项目编号: " + projectNumber);
    }
    
    @Test
    public void testSanitizeMethod() throws Exception {
        YngpCrawler_old crawler = new YngpCrawler_old();
        
        // 使用反射访问私有方法
        Method sanitizeMethod = YngpCrawler_old.class.getDeclaredMethod("sanitize", String.class);
        sanitizeMethod.setAccessible(true);
        
        // 测试文件名清理
        String result1 = (String) sanitizeMethod.invoke(crawler, "KMZC2025-G1-01997-YNZZ-0098");
        Assertions.assertEquals("KMZC2025-G1-01997-YNZZ-0098", result1, "正常项目编号不应该被修改");
        
        String result2 = (String) sanitizeMethod.invoke(crawler, "项目/编号:测试*文件?名");
        Assertions.assertFalse(result2.contains("/"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains(":"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains("*"), "应该移除非法字符");
        Assertions.assertFalse(result2.contains("?"), "应该移除非法字符");
        
        System.out.println("清理后的文件名: " + result2);
    }
}
