<div class="modal-dialog modal-sm"><div class="modal-content"><div class="modal-header bootstrap-dialog-draggable"><div class="bootstrap-dialog-header"><div class="bootstrap-dialog-close-button"><button class="close">×</button></div><div class="bootstrap-dialog-title" id="7a42e95e-d589-4655-97a1-d422ea2b2656_title">文件下载安全验证</div></div></div><div class="modal-body"><div class="bootstrap-dialog-body"><div class="bootstrap-dialog-message"><div>



<div class="row">
	<div class="col-xs-11 col-xs-offset-1">
		<form class="form-horizontal" name="fileSecurityForm" id="fileSecurityForm" action="null">
			<input type="hidden" id="filename" value="鎷涙爣鏂囦欢-鐭冲睆鍘垮叕鍔炲辜鍎垮洯璁惧璐疆 鍙戝竷绋�.docx" style="">
			<input type="hidden" id="authType" value="file_type_public" style="">
			<input type="hidden" id="t" value="1754983182873" style="">
			<input type="hidden" id="fileid" value="29b6670c.1988954ed3a.-76ab" style="">
			<input type="hidden" id="completeurla" value="%2F2025-08-10%2F070001%2F1754813635129.docx" style="">
			<div class="input-group">
				<span class="input-group-addon">验证码</span>
				   <p class="p2_1"><input type="text" id="veryCode" name="veryCode" autocomplete="off" class="ipt_2_1" "="" style="">
					        <a style="text-decoration:underline;margin-left:10px;" onclick="changeImg()" title="点击换一换"><img style=" margin-bottom:10px;" id="imgObj" src="/ynzfcg/login.do?method=verifyCode"></a>
					  </p>
			</div>
			<span class="control-label-text text-danger pull-left errormg"></span>
		</form>
	</div>
</div>
<hr>
<div class="row">
	<div class="col-xs-12">
		<input type="button" class="btn btn-primary pull-right" id="doSubmit" onclick="onSubmitJcapthcha();" data-loading-text="正在验证" value="确定" style="">
			
	</div>
</div>
<script type="text/javascript">
	webAppPath = "/ynzfcg";
	$('#dialog-load').remove();
	
	var timeout = null;
	if (timeout == 0) {
	    fileSecurityDialog.close();
	    BootstrapDialog.alert('请先登录系统', function () {
	        window.location.href = webAppPath + "/login.do?method=begin";
	    });
	}
	//改变验证码信息
	function changeImg(){
		$('.errormg').text('');
	    var imgSrc = jQuery("#imgObj");   
	    var src = imgSrc.attr("src");
	    imgSrc.attr("src",chgUrl(src));
	}   
	//校验验证码     
	function onSubmitJcapthcha(){
		$('.errormg').text('');
	    var code = document.getElementById("veryCode").value;
	    var action="login";
	 	var method="validateCode";
	 	var para="c="+code;
	 	//var result=executeRequest(action,method,para,false)
	 	var result=executeRequestAjaxaa(action,method,para,false);
	 	if(result=="1true"){
	 		var file_id = $("#fileid").val();
	 		var file_name = $("#filename").val();
	 		var completeurl = $("#completeurla").val();
	 		window.location.href = webAppPath+"/filemanager.do?method=downloadFile&file_id="+file_id+"&file_name="+file_name+"&completeurl="+completeurl;
	 	    fileSecurityDialog.close();
	 	}else{
	 		 $('.errormg').text('验证码错误');
		     $("#veryCode").focus();
	 	}
	}	
	//时间戳     
	//为了使每次生成图片不一致，即不让浏览器读缓存，所以需要加上时间戳     
	function chgUrl(url){     
	    var timestamp = (new Date()).valueOf();
	    url = url + "&timestamp=" + timestamp;      
	    return url;     
	}
	
	document.onkeydown = function (e) { // 回车提交表单
		// 兼容FF和IE和Opera
	    var theEvent = window.event || e;
	    var code = theEvent.keyCode || theEvent.which || theEvent.charCode;
	    if (code == 13) {
	        onSubmitJcapthcha();
	        return false;
	    }
	}
	
function executeRequestAjaxaa(actionName, actionMethod, postParameter, isAsynchronism) {

    var strURL = webAppPath + "/" + actionName + ".do";
//    var flag = false;
    if (actionMethod != null && actionMethod != "") {
        strURL += "?method=" + actionMethod;
        //flag = true;
    }
    //增加局部刷新标示符
//    if (flag) {
//        strURL += "&isPartlyRefresh=true";
//    } else {
//        strURL += "?isPartlyRefresh=true";
//    }
    //增加变化的参数（时间+随机数），用于防止代理服务器缓存
    var r = new Date().getTime() + "" + Math.random() * 1000;
    strURL += ("&R=" + r);

    if (postParameter == null || postParameter == "undefined") postParameter = "";

    if (isAsynchronism == null || isAsynchronism == "undefined") isAsynchronism = true;

    var defer, result;
    if (isAsynchronism) {
        defer = $.Deferred();
    }
    $.ajax({
        type: "post",
        url: strURL,
        async: isAsynchronism,
        data: postParameter,
        beforeSend: function (XMLHttpRequest) {
        	XMLHttpRequest.setRequestHeader("useajaxprep","true");
        },
        success: function (data, textStatus) {
            //超时校验
            if (data.indexOf(SESSION_TIMEOUT_TAG) >= 0) {
                //EAPDomain/unieap/pages/login/login.jsp modified at its first line
                window.top.location = window.top.location;
                throw new Error("SESSION_TIMEOUT");
            } else if (typeof data === 'string') {
                if (data.indexOf('错误页面') >= 0 || data.indexOf('error') >=0) {
                    Showloading.close();
                    BootstrapDialog.alert({
                    	title: ConstantMsg.ExceptionTitle,
			        	message: ConstantMsg.ExceptionMessage,
				        type: BootstrapDialog.TYPE_INFO
				    });
                } else if (isAsynchronism) {
                    defer.resolve(data);
                } else {
                    result = data;
                }

            } else if (isAsynchronism) {
                defer.resolve(data);
            } else {
                result = data;
            }

        },
        complete: function (XMLHttpRequest, textStatus) {
        },
        error: function () {
            Showloading.close();
            BootstrapDialog.alert({
                title: ConstantMsg.ExceptionTitle,
		        message: ConstantMsg.ExceptionMessage,
		        type: BootstrapDialog.TYPE_INFO
		    });
        }
    });
    if (isAsynchronism) {
        return defer.promise();
    } else {
        return result;
    }
}
	
</script>
</div></div></div></div><div class="modal-footer" style="display: none;"><div class="bootstrap-dialog-footer"></div></div></div></div>