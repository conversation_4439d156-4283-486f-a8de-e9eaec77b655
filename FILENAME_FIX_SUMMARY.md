# 文件名异常问题解决方案

## 问题分析

在改造后的爬虫运行中发现了两个主要的文件名问题：

1. **乱码文件名**：如 `娌粐鍗忎綔椤圭洰鈥�025骞村嫄鑵婇晣鏇艰春銆侀緳鏋楁潙灏忕粍鍩虹璁炬柦鎻愬崌寤鸿椤圭洰缁堢涓婁紶.docx`
2. **通用文件名**：如 `云南省政府采购网_attachment_1755000985600.doc`

## 根本原因

通过分析HTML样例发现，正确的文件名信息存在于两个地方：

1. **表格显示的文件名**（第3列）：`（定稿）昆明市第一人民医院2025年上半年自筹经费设备购置项目（第二批）.doc`
2. **下载链接的JavaScript函数参数**：
   ```javascript
   javascript:download('1ef04861.198895699f8.-7455','/2025-08-10/070001/1754809621604.doc','（定稿）昆明市第一人民医院2025年上半年自筹经费设备购置项目（第二批）.doc')
   ```

原有的文件名提取逻辑没有充分利用这些信息，导致使用了不正确的文件名。

## 解决方案

### 1. 新增专门的文件名提取方法

**`extractFileNameFromTableRow(Locator downloadLink)`**
- 优先从下载链接的 `javascript:download()` 函数第三个参数中提取文件名
- 如果提取失败，从表格行的文件名称列（第3列）中提取
- 双重保障确保能获取到正确的文件名

**`extractFileNameFromJavaScriptDownload(String jsCode)`**
- 专门解析 `javascript:download('id','/path','filename.ext')` 格式
- 正确处理单引号和双引号
- 提取第三个参数作为真实文件名

### 2. 改进下载流程

**修改 `processFileListTable()` 方法**
- 在处理每个下载链接前，先提取正确的文件名
- 使用新的 `attemptClickDownloadMultipleWithFileName()` 方法传递正确的文件名

**新增 `attemptClickDownloadMultipleWithFileName()` 方法**
- 接受预设的文件名参数
- 优先使用传入的正确文件名
- 如果传入文件名为空，回退到原有的提取逻辑

### 3. 文件名优先级策略

改进后的文件名确定优先级：

1. **预设的正确文件名**（从表格或JavaScript函数中提取）
2. **从下载URL查询参数中提取**
3. **从下载URL路径中提取**
4. **浏览器建议的文件名**
5. **默认生成的文件名**

## 技术实现细节

### JavaScript函数解析

```java
private String extractFileNameFromJavaScriptDownload(String jsCode) {
    // 解析 javascript:download('id','/path','filename.ext')
    String params = jsCode.substring(startParen + 1, endParen);
    String[] args = params.split(",");
    
    if (args.length >= 3) {
        String fileName = args[2].trim();
        // 移除引号
        if ((fileName.startsWith("'") && fileName.endsWith("'")) ||
            (fileName.startsWith("\"") && fileName.endsWith("\""))) {
            fileName = fileName.substring(1, fileName.length() - 1);
        }
        return fileName;
    }
}
```

### 表格行文件名提取

```java
private String extractFileNameFromTableRow(Locator downloadLink) {
    // 1. 优先从JavaScript函数中提取
    String href = downloadLink.getAttribute("href");
    if (href != null && href.startsWith("javascript:download")) {
        String fileName = extractFileNameFromJavaScriptDownload(href);
        if (fileName != null && !fileName.isBlank()) {
            return fileName;
        }
    }
    
    // 2. 从表格单元格中提取
    Locator row = downloadLink.locator("xpath=ancestor::tr");
    Locator fileNameCell = row.locator("td").nth(2); // 第3列
    String cellText = fileNameCell.textContent();
    return cellText.trim();
}
```

## 测试验证

创建了专门的测试用例验证文件名提取功能：

- ✅ JavaScript函数解析测试通过
- ✅ 单引号和双引号处理测试通过
- ✅ 中文文件名处理测试通过
- ✅ 边界情况处理测试通过

## 预期效果

改进后，文件名应该变为：
- **之前**：`云南省政府采购网_attachment_1755000985600.doc`
- **之后**：`KMZC2025-G1-01997-YNZZ-0098_（定稿）昆明市第一人民医院2025年上半年自筹经费设备购置项目（第二批）.doc`

这样文件名既包含项目编号，又包含正确的原始文件名，便于识别和管理。

## 兼容性

- 保持了原有的所有下载逻辑
- 新的文件名提取是增强功能，不影响现有流程
- 如果新的提取方法失败，会自动回退到原有逻辑
